# Kafka 数据导出工具

这是一个用于从Kafka导出设备数据的工具集。包含以下组件：

## 组件说明

1. **producer**：数据生产者，向Kafka发送设备数据和设备信息
3. **exporter**：服务器进程版数据导出工具，监听设备信息主题，自动导出相关设备数据

## 依赖项

- librdkafka (Kafka C/C++客户端库)
- protobuf (Google Protocol Buffers)
- n<PERSON>hmann-json (用于JSON处理)
- pthread, zlib, rt, dl等系统库

## 编译方法

### 编译全部组件

```bash
make
```

### 编译单个组件

```bash
make exporter       # 只编译exporter服务
make producer       # 只编译producer工具
```

### 清理编译文件

```bash
make clean
```

## 使用方法

### exporter服务

exporter服务是一个持续运行的服务进程，它会监听Kafka的设备信息主题(fd_info)，根据收到的设备信息自动导出对应的设备数据。

```bash
./bin/exporter [选项]
```

选项：
- `-v, --verbose=LEVEL`：详细程度 (0-4, 默认: 0)
- `-o, --output-dir=DIR`：输出目录 (默认: ../output/exporter)
- `-a, --auto-fill`：自动填充缺失帧 (默认: 禁用)
- `-n, --no-sort`：不排序消息 (默认: 启用排序)
- `-s, --state-file=FILE`：状态文件路径
- `-l, --log-file=FILE`：日志文件路径
- `-L, --log-level=LEVEL`：日志级别 (0-4, 默认: 2)
- `-d, --daemon`：作为守护进程运行 (默认: 禁用)
- `-p, --pid-file=FILE`：PID文件路径
- `-k, --kafka-brokers=LIST`：Kafka服务器地址列表
- `-m, --max-tasks=NUM`：最大并发任务数 (默认: 64)
- `-r, --retry-count=NUM`：重试次数 (默认: 3)
- `-R, --retry-delay=MS`：重试延迟(毫秒) (默认: 5000)
- `-h, --help`：显示帮助信息

示例：
```bash
./bin/exporter -v 2 -k "localhost:9092" -o "/data/output" -d
```



### producer工具

producer是数据生产者，用于向Kafka发送模拟的设备数据和设备信息。

```bash
./bin/producer [选项] [芯片ID]
```

选项：
- `-b, --brokers=LIST`：指定Kafka服务器地址
- `-h, --help`：显示帮助信息

## 输出目录结构

数据将被保存在以下结构的目录中：

```
output_dir/
  ├── <芯片ID1>/
  │     └── <芯片ID1>_<datetime>.ulg
  ├── <芯片ID2>/
  │     └── <芯片ID2>_<datetime>.ulg
  └── ...
```

## 服务控制

exporter服务支持以下信号控制：

- `SIGINT` 或 `SIGTERM`：优雅关闭服务
- `SIGUSR1`：保存当前状态
- `SIGUSR2`：重新加载配置（未实现） 