# Kafka Exporter 部署指南

本文档提供了将 Kafka Exporter 部署为系统服务的详细说明。

## 概述

Kafka Exporter 是一个持续运行的服务进程，它监听 Kafka 的设备信息主题(fd_info)，根据收到的设备信息自动导出对应的设备数据到指定目录。

## 系统要求

- Linux 系统 (CentOS 7+/Ubuntu 18.04+)
- Kafka 集群连接信息
- 至少 1GB 可用内存
- 足够的磁盘空间用于存储导出的数据

## 部署步骤

### 1. 准备工作

首先创建必要的用户和目录：

```bash
# 创建服务用户（如果尚不存在）
sudo useradd -r -s /sbin/nologin kafka

# 创建目录结构
sudo mkdir -p /opt/kafka/exporter/bin
sudo mkdir -p /opt/kafka/exporter/output
sudo mkdir -p /var/log/kafka
sudo mkdir -p /etc/kafka/exporter
```

### 2. 复制文件

将必要的文件复制到服务器：

```bash
# 复制可执行文件
sudo cp bin/exporter /opt/kafka/exporter/bin/

# 复制服务脚本
sudo cp tools/exporter.service /etc/systemd/system/
# 或者对于使用SysV init的系统：
sudo cp tools/exporter-service.sh /etc/init.d/exporter
sudo chmod +x /etc/init.d/exporter
```

### 3. 设置权限

确保正确的文件权限：

```bash
sudo chown -R kafka:kafka /opt/kafka/exporter
sudo chown -R kafka:kafka /var/log/kafka
sudo chmod 755 /opt/kafka/exporter/bin/exporter
```

### 4. 配置服务

#### 使用 systemd (推荐)

编辑服务配置文件，根据需要修改 Kafka 连接信息和其他参数：

```bash
sudo vim /etc/systemd/system/exporter.service
```

主要配置参数：
- `ExecStart`: 包含启动命令和参数
  - `-v`: 日志详细度级别 (0-3)
  - `-k`: Kafka 服务器地址
  - `-o`: 输出目录
  - `-l`: 日志文件路径
  - `-p`: PID 文件路径
  - `-i`: 设备信息 CSV 文件路径

#### 使用 SysV init

如果使用传统的 init 脚本：

```bash
sudo vim /etc/init.d/exporter
```

修改脚本顶部的配置变量，特别是：
- `KAFKA_BROKER`: Kafka 服务器地址
- `KAFKA_GROUP`: 消费者组 ID
- `KAFKA_TOPIC`: 设备信息主题

### 5. 启动服务

#### 使用 systemd

```bash
# 重新加载服务定义
sudo systemctl daemon-reload

# 启用服务（开机自启动）
sudo systemctl enable exporter.service

# 启动服务
sudo systemctl start exporter.service

# 检查状态
sudo systemctl status exporter.service
```

#### 使用 SysV init

```bash
# 配置服务自启动
sudo chkconfig --add exporter
sudo chkconfig exporter on

# 启动服务
sudo service exporter start

# 检查状态
sudo service exporter status
```

## 验证部署

部署完成后，可通过以下方式验证服务是否正常运行：

1. 检查服务状态：
   ```bash
   sudo systemctl status exporter.service
   # 或
   sudo service exporter status
   ```

2. 检查日志文件：
   ```bash
   sudo tail -f /var/log/kafka/exporter.log
   ```

3. 检查输出目录是否有文件生成：
   ```bash
   ls -la /opt/kafka/exporter/output
   ```

## 服务管理

### 停止服务

```bash
sudo systemctl stop exporter.service
# 或
sudo service exporter stop
```

### 重启服务

```bash
sudo systemctl restart exporter.service
# 或
sudo service exporter restart
```

### 发送信号

exporter 支持通过信号控制：

```bash
# 获取 PID
PID=$(cat /var/run/exporter.pid)

# 发送状态转储信号
kill -SIGUSR1 $PID

# 发送重新加载配置信号
kill -SIGUSR2 $PID
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查日志文件 `/var/log/kafka/exporter.log`
   - 确保 Kafka 服务可访问
   - 验证文件权限

2. **没有数据导出**
   - 检查 Kafka 主题是否有消息
   - 确认消费者组 ID 是否正确
   - 验证网络连接是否正常

3. **服务崩溃**
   - 检查日志是否有 Java 异常
   - 确认系统资源是否充足
   - 可能需要调整 JVM 参数

### 日志分析

查看最近的错误：

```bash
grep ERROR /var/log/kafka/exporter.log
```

## 升级说明

升级服务时，请按照以下步骤：

1. 停止服务：
   ```bash
   sudo systemctl stop exporter.service
   ```

2. 备份现有文件：
   ```bash
   sudo cp /opt/kafka/exporter/bin/exporter /opt/kafka/exporter/bin/exporter.bak
   ```

3. 复制新版本：
   ```bash
   sudo cp new-version/exporter /opt/kafka/exporter/bin/
   sudo chmod 755 /opt/kafka/exporter/bin/exporter
   ```

4. 重新启动服务：
   ```bash
   sudo systemctl start exporter.service
   ```

## 安全注意事项

- 确保 Kafka 连接使用合适的认证和加密
- 限制 `/opt/kafka/exporter` 目录的访问权限
- 定期检查和轮换日志文件

## 支持与帮助

如有问题或需要支持，请联系：<EMAIL> 