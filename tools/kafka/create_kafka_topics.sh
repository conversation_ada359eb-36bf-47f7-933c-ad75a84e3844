#!/bin/bash

# Kafka主题创建脚本 - 创建32个主题，每个主题200个分区，以及一个fd_info主题

# Kafka配置
BROKER="172.70.74.212:9092"
TOPIC_PREFIX="fd_log_"
TOPIC_COUNT=32
PARTITIONS=200
REPLICATION_FACTOR=2  # 根据您的Kafka集群规模调整
INFO_TOPIC="fd_info"

echo "===== 开始创建Kafka主题 ====="
echo "主题前缀: $TOPIC_PREFIX"
echo "主题数量: $TOPIC_COUNT"
echo "每个主题的分区数: $PARTITIONS"
echo "副本因子: $REPLICATION_FACTOR"
echo "信息主题: $INFO_TOPIC"
echo "=========================="

# 首先创建信息主题
echo "正在创建信息主题: $INFO_TOPIC (分区数: $PARTITIONS)"
kafka-topics.sh --bootstrap-server $BROKER --create \
    --topic $INFO_TOPIC \
    --partitions $PARTITIONS \
    --replication-factor $REPLICATION_FACTOR

if [ $? -eq 0 ]; then
    echo "✅ 信息主题 $INFO_TOPIC 创建成功"
else
    echo "❌ 信息主题 $INFO_TOPIC 创建失败"
fi

# 创建所有数据主题
for ((i=0; i<$TOPIC_COUNT; i++)); do
    # 将i转换为两位十六进制
    SUFFIX=$(printf "%02x" $i)
    TOPIC="${TOPIC_PREFIX}${SUFFIX}"
    
    echo "正在创建主题: $TOPIC (分区数: $PARTITIONS)"
    
    # 创建主题
    kafka-topics.sh --bootstrap-server $BROKER --create \
        --topic $TOPIC \
        --partitions $PARTITIONS \
        --replication-factor $REPLICATION_FACTOR \
        --config retention.ms=604800000 \
        --config cleanup.policy=delete
    
    # 检查创建结果
    if [ $? -eq 0 ]; then
        echo "✅ 主题 $TOPIC 创建成功"
    else
        echo "❌ 主题 $TOPIC 创建失败"
    fi
done

# 列出所有已创建的主题
echo -e "\n===== 已创建的主题列表 ====="
kafka-topics.sh --bootstrap-server $BROKER --list | grep -E "$TOPIC_PREFIX|$INFO_TOPIC"

# 显示信息主题的详细信息
echo -e "\n===== 信息主题详情 ($INFO_TOPIC) ====="
kafka-topics.sh --bootstrap-server $BROKER --describe --topic $INFO_TOPIC

# 显示第一个数据主题的详细信息作为示例
SAMPLE_TOPIC="${TOPIC_PREFIX}00"
echo -e "\n===== 数据主题示例详情 ($SAMPLE_TOPIC) ====="
kafka-topics.sh --bootstrap-server $BROKER --describe --topic $SAMPLE_TOPIC

echo -e "\n===== 主题创建完成! 共创建 $TOPIC_COUNT 个数据主题和1个信息主题，每个主题 $PARTITIONS 个分区 =====" 