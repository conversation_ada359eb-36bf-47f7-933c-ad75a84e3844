#!/bin/bash

# Kafka主题清理脚本
# 用于删除fd_log_xx系列主题（共32个）以及fd_info主题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 默认参数
KAFKA_BROKERS="172.70.74.212:9092"
TOPIC_PREFIX="fd_log_"
TOPIC_COUNT=32
INFO_TOPIC="fd_info"
DRY_RUN=false
FORCE=false

# 帮助信息
show_help() {
    echo -e "${BLUE}Kafka主题清理工具${NC}"
    echo "用于删除fd_log_xx系列主题和fd_info主题"
    echo
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -b, --brokers BROKERS        指定Kafka服务器地址（默认：$KAFKA_BROKERS）"
    echo "  -p, --prefix PREFIX          主题前缀（默认：$TOPIC_PREFIX）"
    echo "  -c, --count COUNT            主题数量（默认：$TOPIC_COUNT）"
    echo "  -i, --info TOPIC             信息主题名称（默认：$INFO_TOPIC）"
    echo "  -d, --dry-run                仅显示将执行的操作，不实际执行"
    echo "  -f, --force                  强制执行，不提示确认"
    echo "  -h, --help                   显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 -b localhost:9092         使用本地Kafka服务器"
    echo "  $0 --dry-run                 显示将要执行的操作但不实际执行"
    echo "  $0 -p my_topic_ -c 8         清理my_topic_00到my_topic_07的8个主题"
    echo "  $0 -f                        强制执行，不提示确认"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--brokers)
            KAFKA_BROKERS="$2"
            shift 2
            ;;
        -p|--prefix)
            TOPIC_PREFIX="$2"
            shift 2
            ;;
        -c|--count)
            TOPIC_COUNT="$2"
            shift 2
            ;;
        -i|--info)
            INFO_TOPIC="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}错误: 未知选项 $1${NC}" >&2
            show_help
            exit 1
            ;;
    esac
done

# 检查kafka-topics命令是否可用
check_kafka_cli() {
    if ! command -v kafka-topics.sh &> /dev/null; then
        echo -e "${RED}错误: 未找到kafka-topics.sh命令${NC}" >&2
        echo "请确保Kafka命令行工具已安装并添加到PATH中，或使用完整路径执行此脚本" >&2
        exit 1
    fi
}

# 列出要清理的主题
list_topics() {
    echo -e "${BLUE}将清除以下主题:${NC}"
    for i in $(seq 0 $((TOPIC_COUNT-1))); do
        # 使用十六进制格式
        TOPIC_SUFFIX=$(printf "%02x" $i)
        echo "  ${TOPIC_PREFIX}${TOPIC_SUFFIX}"
    done
    echo "  ${INFO_TOPIC} (信息主题)"
    echo
}

# 确认操作
confirm_operation() {
    if [ "$FORCE" = true ]; then
        return 0
    fi
    
    echo -e "${YELLOW}警告: 此操作将删除上述主题，所有数据将丢失!${NC}"
    read -p "确定要继续吗? [y/N] " response
    case "$response" in
        [yY][eE][sS]|[yY]) 
            return 0
            ;;
        *)
            echo "操作已取消"
            exit 0
            ;;
    esac
}

# 检查主题是否存在
topic_exists() {
    local topic=$1
    kafka-topics.sh --bootstrap-server "$KAFKA_BROKERS" --describe --topic "$topic" &>/dev/null
    return $?
}

# 删除主题
delete_topic() {
    local topic=$1
    echo -e "删除主题: ${YELLOW}$topic${NC}"
    if [ "$DRY_RUN" = false ]; then
        kafka-topics.sh --bootstrap-server "$KAFKA_BROKERS" --delete --topic "$topic"
        # 等待主题实际删除
        sleep 2
    fi
}

# 执行主要流程
main() {
    echo -e "${BLUE}==== Kafka主题清理工具 ====${NC}"
    echo "Kafka服务器: $KAFKA_BROKERS"
    echo "主题前缀: $TOPIC_PREFIX"
    echo "主题数量: $TOPIC_COUNT"
    echo "信息主题: $INFO_TOPIC"
    echo "模拟执行: $DRY_RUN"
    echo

    if [ "$DRY_RUN" = true ]; then
        echo -e "${YELLOW}注意: 这是一次模拟运行，不会实际执行任何操作${NC}"
        echo
    fi

    # 如果不是模拟运行，检查kafka-topics命令
    if [ "$DRY_RUN" = false ]; then
        check_kafka_cli
    fi

    # 列出要处理的主题
    list_topics

    # 确认操作
    confirm_operation

    echo -e "${BLUE}开始清除主题...${NC}"
    
    # 首先处理信息主题
    if [ "$DRY_RUN" = false ]; then
        if topic_exists "$INFO_TOPIC"; then
            delete_topic "$INFO_TOPIC"
        else
            echo -e "主题 ${YELLOW}$INFO_TOPIC${NC} 不存在，跳过"
        fi
    else
        delete_topic "$INFO_TOPIC"
    fi
    
    echo
    
    # 处理每个数据主题
    for i in $(seq 0 $((TOPIC_COUNT-1))); do
        # 使用十六进制格式生成主题后缀
        TOPIC_SUFFIX=$(printf "%02x" $i)
        topic="${TOPIC_PREFIX}${TOPIC_SUFFIX}"
        
        # 检查主题是否存在
        if [ "$DRY_RUN" = false ]; then
            if topic_exists "$topic"; then
                delete_topic "$topic"
            else
                echo -e "主题 ${YELLOW}$topic${NC} 不存在，跳过"
            fi
        else
            # 在模拟模式下，假设主题存在
            delete_topic "$topic"
        fi
        
        echo
    done

    echo -e "${GREEN}清除操作完成!${NC}"
    if [ "$DRY_RUN" = true ]; then
        echo -e "${YELLOW}注意: 这是一次模拟运行，没有实际执行任何操作${NC}"
    fi
}

# 执行主程序
main
