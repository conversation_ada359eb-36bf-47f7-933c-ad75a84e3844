#!/bin/bash

echo "编译fd_info诊断工具..."

# 创建输出目录
mkdir -p tools/output

# 编译诊断工具
g++ -Wall -g -O2 -std=c++17 -pthread -Isrc -Ilibs/include -Wno-deprecated-declarations \
    -o tools/output/fd_info_diagnostic \
    tools/fd_info_diagnostic.cpp \
    src/common.cpp \
    -Llibs/lib -lrdkafka++ -pthread -lcrypto

if [ $? -eq 0 ]; then
    echo "✅ 编译成功！"
    echo "使用方法: ./tools/output/fd_info_diagnostic <kafka_brokers>"
    echo "例如: ./tools/output/fd_info_diagnostic 119.145.39.253:9094"
else
    echo "❌ 编译失败！"
    exit 1
fi
