#!/bin/bash

# 压力测试脚本：启动多个producer和多个exporter

# 检查producer和exporter可执行文件是否存在
if [ ! -f ../../bin/producer ] || [ ! -f ../../bin/exporter_pro ]; then
    echo "错误: producer或exporter可执行文件不存在，请先编译项目"
    echo "运行命令: make clean && make"
    exit 1
fi

# 清理旧的测试目录
rm -rf ../../output/test_results
mkdir -p ../../output/test_results

# 创建测试日志文件
TEST_LOG="../../output/test_results/stress_test_$(date +%Y%m%d%H%M%S).log"
PRODUCER_PIDS="../../output/test_results/producer_pids.txt"
CONSUMER_PIDS="../../output/test_results/exporter_pids.txt"
# 确保PID文件为空
> $PRODUCER_PIDS
> $CONSUMER_PIDS

# 基础chip_id前缀
BASE_CHIP_ID="b124ee90bffe9"

# 要启动的producer数量
PRODUCER_COUNT=200
# 要启动的exporter数量
CONSUMER_COUNT=0

# 添加最大运行时间（例如10分钟）
MAX_RUN_TIME=600  # 秒

# 增加系统资源限制
ulimit -n 65536  # 进一步增加文件描述符限制

# 清空日志文件
echo "开始压力测试: $(date)" > $TEST_LOG
echo "启动 $PRODUCER_COUNT 个producer实例和 $CONSUMER_COUNT 个exporter" >> $TEST_LOG

# 先启动多个exporter
echo "启动 $CONSUMER_COUNT 个exporter..."
for i in $(seq 0 $((CONSUMER_COUNT-1))); do
    # 生成exporter ID
    NUM=$(printf "%02d" $i)
    
    # 启动exporter，将输出重定向到各自的日志文件
    ../../bin/exporter_pro -v -R -b ************:9092 > "../../output/test_results/exporter_pro_${NUM}_output.log" 2>&1 &
    CONSUMER_PID=$!
    
    # 记录PID (确保非空)
    if [ -n "$CONSUMER_PID" ] && [ "$CONSUMER_PID" -gt 0 ]; then
        echo "$NUM:$CONSUMER_PID" >> $CONSUMER_PIDS
        echo "启动exporter #$i, PID: $CONSUMER_PID" | tee -a $TEST_LOG
    else
        echo "警告: exporter_pro #$i 启动失败或PID无效" | tee -a $TEST_LOG
    fi
    
    # 短暂睡眠，避免启动过于集中
    sleep 1
done

# 短暂等待让exporter启动
sleep 5

# 按顺序启动producer，每个使用不同的chip_id
echo "开始启动producer实例..."
for i in $(seq 0 $((PRODUCER_COUNT-1))); do
    # 生成3位数的序号，不足3位前面补0
    NUM=$(printf "%03d" $i)
    CHIP_ID="${BASE_CHIP_ID}${NUM}"
    
    # 启动producer，将输出重定向到各自的日志文件
    ../../bin/producer $CHIP_ID -b ************:9092 > "../../output/test_results/producer_${NUM}.log" 2>&1 &
    PRODUCER_PID=$!
    
    # 记录PID (确保非空)
    if [ -n "$PRODUCER_PID" ] && [ "$PRODUCER_PID" -gt 0 ]; then
        echo "$NUM:$PRODUCER_PID" >> $PRODUCER_PIDS
        echo "启动producer #$i, chip_id: $CHIP_ID, PID: $PRODUCER_PID" | tee -a $TEST_LOG
    else
        echo "警告: producer #$i 启动失败或PID无效" | tee -a $TEST_LOG
    fi
    
    # 短暂睡眠，避免启动过于集中
    sleep 0.1
done

echo "所有producer已启动，等待测试完成..." | tee -a $TEST_LOG
echo "按Ctrl+C停止测试" | tee -a $TEST_LOG

# 定义清理函数
cleanup() {
    echo -e "\n正在清理进程..." | tee -a $TEST_LOG
    
    # 终止所有exporter
    if [ -f $CONSUMER_PIDS ]; then
        echo "终止exporter进程..." | tee -a $TEST_LOG
        while IFS= read -r line || [ -n "$line" ]; do
            # 跳过空行
            [ -z "$line" ] && continue
            
            # 提取PID并验证
            PID=$(echo "$line" | cut -d':' -f2)
            if [ -n "$PID" ] && [ "$PID" -gt 0 ]; then
                if ps -p $PID > /dev/null 2>&1; then
                    echo "终止exporter进程 (PID: $PID)" | tee -a $TEST_LOG
                    kill -SIGINT $PID
                fi
            fi
        done < $CONSUMER_PIDS
    fi
    
    # 终止所有producer
    if [ -f $PRODUCER_PIDS ]; then
        echo "终止producer进程..." | tee -a $TEST_LOG
        while IFS= read -r line || [ -n "$line" ]; do
            # 跳过空行
            [ -z "$line" ] && continue
            
            # 提取PID并验证
            PID=$(echo "$line" | cut -d':' -f2)
            if [ -n "$PID" ] && [ "$PID" -gt 0 ]; then
                if ps -p $PID > /dev/null 2>&1; then
                    echo "终止producer进程 (PID: $PID)" | tee -a $TEST_LOG
                    kill -9 $PID
                fi
            fi
        done < $PRODUCER_PIDS
    fi
    
    
    # 查找并终止任何可能遗漏的producer进程
    echo "检查并终止任何遗漏的producer进程..." | tee -a $TEST_LOG
    for PID in $(pgrep -f "./producer b124ee90bffe9"); do
        if ps -p $PID > /dev/null 2>&1; then
            echo "终止遗漏的producer进程 (PID: $PID)" | tee -a $TEST_LOG
            kill -9 $PID
        fi
    done
    
    if [ "$CONSUMER_COUNT" -gt 0 ]; then
        # 查找并终止任何可能遗漏的exporter进程
        echo "检查并终止任何遗漏的exporter进程..." | tee -a $TEST_LOG
        for PID in $(pgrep -f "./exporter_pro"); do
            if ps -p $PID > /dev/null 2>&1; then
                echo "终止遗漏的exporter进程 (PID: $PID)" | tee -a $TEST_LOG
                kill -SIGINT $PID
            fi
            done
    fi
    
    echo "清理完成。" | tee -a $TEST_LOG
    echo "测试结果和日志保存在 test_results 目录" | tee -a $TEST_LOG
    echo "测试结束时间: $(date)" >> $TEST_LOG
    
    exit 0
}

# 捕获中断信号
trap cleanup SIGINT SIGTERM

# 在主循环中添加超时检查
start_time=$(date +%s)
while true; do
    current_time=$(date +%s)
    elapsed=$((current_time - start_time))
    
    if [ $elapsed -ge $MAX_RUN_TIME ]; then
        echo "达到最大运行时间（${MAX_RUN_TIME}秒），正在停止测试..." | tee -a $TEST_LOG
        break
    fi
    
    # 检查还有多少producer进程在运行
    RUNNING=0
    if [ -f $PRODUCER_PIDS ]; then
        while IFS= read -r line || [ -n "$line" ]; do
            # 跳过空行
            [ -z "$line" ] && continue
            
            # 提取PID并验证
            PID=$(echo "$line" | cut -d':' -f2)
            if [ -n "$PID" ] && [ "$PID" -gt 0 ]; then
                if ps -p $PID > /dev/null 2>&1; then
                    RUNNING=$((RUNNING+1))
                fi
            fi
        done < $PRODUCER_PIDS
    fi
    
    echo "当前还有 $RUNNING 个producer进程运行中... (已运行 ${elapsed} 秒)" | tee -a $TEST_LOG
    sleep 3
done

# 测试完成，清理进程
cleanup 