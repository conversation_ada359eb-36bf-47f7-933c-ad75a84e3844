#!/bin/bash

# 测试不同消费模式的脚本

echo "=== Kafka消费模式测试工具 ==="
echo ""

# 检查exporter是否存在
if [ ! -f "./bin/exporter" ]; then
    echo "❌ 错误: ./bin/exporter 不存在，请先编译"
    exit 1
fi

# 显示帮助信息
show_help() {
    echo "用法: $0 [模式]"
    echo ""
    echo "可用的消费模式:"
    echo "  latest    - 从最新位置开始消费 (默认，适合生产环境)"
    echo "  earliest  - 从最早位置开始消费 (处理所有历史数据)"
    echo "  stored    - 使用存储的offset继续消费 (断点续传)"
    echo ""
    echo "示例:"
    echo "  $0 latest     # 只处理新数据，跳过历史数据"
    echo "  $0 earliest   # 处理所有历史数据（可能很慢）"
    echo "  $0 stored     # 从上次停止的位置继续"
    echo ""
    echo "注意:"
    echo "  - latest模式适合生产环境，避免处理大量历史数据"
    echo "  - earliest模式会处理所有历史数据，可能需要很长时间"
    echo "  - stored模式使用固定消费者组，支持断点续传"
}

# 检查参数
if [ $# -eq 0 ]; then
    echo "请选择消费模式:"
    echo "1) latest   - 只处理新数据 (推荐)"
    echo "2) earliest - 处理所有历史数据"
    echo "3) stored   - 断点续传"
    echo "4) 显示帮助"
    echo ""
    read -p "请输入选择 (1-4): " choice
    
    case $choice in
        1) MODE="latest" ;;
        2) MODE="earliest" ;;
        3) MODE="stored" ;;
        4) show_help; exit 0 ;;
        *) echo "❌ 无效选择"; exit 1 ;;
    esac
elif [ "$1" = "help" ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
else
    MODE="$1"
fi

# 验证模式
case $MODE in
    latest|earliest|stored)
        echo "✅ 选择的消费模式: $MODE"
        ;;
    *)
        echo "❌ 错误: 无效的消费模式 '$MODE'"
        echo "有效模式: latest, earliest, stored"
        exit 1
        ;;
esac

echo ""

# 根据模式显示说明
case $MODE in
    latest)
        echo "📋 模式说明: latest"
        echo "   - 从最新位置开始消费"
        echo "   - 跳过所有历史数据"
        echo "   - 适合生产环境，避免处理积压的历史数据"
        echo "   - 使用唯一消费者组，不会影响其他消费者"
        ;;
    earliest)
        echo "📋 模式说明: earliest"
        echo "   - 从最早位置开始消费"
        echo "   - 处理所有历史数据"
        echo "   - ⚠️  警告: 如果有20天的历史数据，可能需要很长时间"
        echo "   - 使用唯一消费者组，避免影响其他消费者"
        ;;
    stored)
        echo "📋 模式说明: stored"
        echo "   - 使用存储的offset继续消费"
        echo "   - 支持断点续传"
        echo "   - 使用固定消费者组 'exporter_group_stable'"
        echo "   - 如果是首次运行，会从earliest开始"
        ;;
esac

echo ""

# 确认执行
if [ "$MODE" = "earliest" ]; then
    echo "⚠️  警告: earliest模式会处理所有历史数据！"
    echo "   如果Kafka中有大量历史数据，这可能需要很长时间。"
    echo ""
    read -p "确定要继续吗? (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "已取消"
        exit 0
    fi
fi

echo "🚀 启动exporter，消费模式: $MODE"
echo ""

# 构建命令
CMD="./bin/exporter --consume-mode=$MODE --log-level=3"

echo "执行命令: $CMD"
echo ""
echo "按 Ctrl+C 停止程序"
echo "=========================="

# 执行命令
exec $CMD
