#!/bin/bash

# 重置消费者组offset的脚本

KAFKA_BROKERS="119.145.39.253:9094"
CONSUMER_GROUP="exporter_group"
TOPIC="fd_info"

echo "重置消费者组offset..."
echo "Kafka服务器: $KAFKA_BROKERS"
echo "消费者组: $CONSUMER_GROUP"
echo "主题: $TOPIC"

# 检查是否有kafka-consumer-groups工具
if ! command -v kafka-consumer-groups &> /dev/null; then
    echo "❌ kafka-consumer-groups工具未找到"
    echo "请安装Kafka客户端工具或使用以下命令："
    echo "kafka-consumer-groups --bootstrap-server $KAFKA_BROKERS --group $CONSUMER_GROUP --reset-offsets --to-earliest --topic $TOPIC --execute"
    exit 1
fi

# 显示当前offset
echo "当前消费者组offset状态:"
kafka-consumer-groups --bootstrap-server $KAFKA_BROKERS --group $CONSUMER_GROUP --describe

echo ""
echo "重置offset到最早位置..."

# 重置offset到最早位置
kafka-consumer-groups --bootstrap-server $KAFKA_BROKERS \
    --group $CONSUMER_GROUP \
    --reset-offsets \
    --to-earliest \
    --topic $TOPIC \
    --execute

if [ $? -eq 0 ]; then
    echo "✅ offset重置成功！"
    echo ""
    echo "重置后的offset状态:"
    kafka-consumer-groups --bootstrap-server $KAFKA_BROKERS --group $CONSUMER_GROUP --describe
else
    echo "❌ offset重置失败！"
    exit 1
fi
