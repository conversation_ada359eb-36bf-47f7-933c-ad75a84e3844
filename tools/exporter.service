[Unit]
Description=Kafka Data Exporter Service
After=network.target
Wants=kafka.service

[Service]
Type=simple
User=kafka
Group=kafka
WorkingDirectory=/opt/kafka/exporter
ExecStart=/opt/kafka/exporter/bin/exporter \
  -v 1 \
  -k "localhost:9092" \
  -o "/opt/kafka/exporter/output" \
  -l "/var/log/kafka/exporter.log" \
  -p "/var/run/exporter.pid" \
  -i "/opt/kafka/exporter/output/device_info.csv"
SuccessExitStatus=0 143
Restart=always
RestartSec=30
StartLimitBurst=3
StartLimitInterval=60
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=32768
LimitCORE=infinity

# System resource control
CPUAccounting=true
CPUQuota=90%
MemoryAccounting=true
MemoryLimit=1G
IOAccounting=true

[Install]
WantedBy=multi-user.target 