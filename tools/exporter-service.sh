#!/bin/bash
#
# exporter - Kafka数据导出服务脚本
#
# chkconfig: 2345 80 20
# description: Kafka数据导出服务, 实时监控设备数据并导出为文件
# processname: exporter
# pidfile: /var/run/exporter.pid
# config: /etc/kafka/exporter/config.conf

# 服务配置
SERVICE_NAME="exporter"
APP_DIR="/opt/kafka/exporter"
BIN_FILE="$APP_DIR/bin/exporter"
PID_FILE="/var/run/exporter.pid"
LOG_DIR="/var/log/kafka"
LOG_FILE="$LOG_DIR/exporter.log"
OUTPUT_DIR="$APP_DIR/output"
DEVICE_INFO_CSV="$OUTPUT_DIR/device_info.csv"

# Kafka配置
KAFKA_BROKER="localhost:9092"
KAFKA_GROUP="exporter-group"
KAFKA_TOPIC="fd_info"

# 用户和组
USER="kafka"
GROUP="kafka"

# 运行参数
VERBOSE_LEVEL=1

# 脚本函数
start() {
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null; then
            echo "$SERVICE_NAME 已经在运行中，PID: $PID"
            return 1
        else
            rm -f $PID_FILE
        fi
    fi
    
    echo "正在启动 $SERVICE_NAME..."
    
    # 确保目录存在
    mkdir -p $LOG_DIR
    mkdir -p $OUTPUT_DIR
    chown -R $USER:$GROUP $LOG_DIR
    chown -R $USER:$GROUP $OUTPUT_DIR
    
    # 启动应用
    su - $USER -c "nohup $BIN_FILE -v $VERBOSE_LEVEL -k $KAFKA_BROKER -o $OUTPUT_DIR -l $LOG_FILE -p $PID_FILE -i $DEVICE_INFO_CSV -g $KAFKA_GROUP -t $KAFKA_TOPIC -d > /dev/null 2>&1 &"
    
    sleep 2
    
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null; then
            echo "$SERVICE_NAME 启动成功，PID: $PID"
            return 0
        fi
    fi
    
    echo "$SERVICE_NAME 启动失败"
    return 1
}

stop() {
    if [ ! -f $PID_FILE ]; then
        echo "$SERVICE_NAME 未运行。"
        return 0
    fi
    
    PID=$(cat $PID_FILE)
    
    echo "停止 $SERVICE_NAME..."
    kill -15 $PID
    
    # 等待进程结束
    TIMEOUT=30
    while ps -p $PID > /dev/null; do
        if [ $TIMEOUT -le 0 ]; then
            echo "强制终止 $SERVICE_NAME 进程..."
            kill -9 $PID
            break
        fi
        
        echo "等待 $SERVICE_NAME 停止...$TIMEOUT"
        sleep 1
        TIMEOUT=$((TIMEOUT - 1))
    done
    
    rm -f $PID_FILE
    echo "$SERVICE_NAME 已停止"
    return 0
}

status() {
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null; then
            echo "$SERVICE_NAME 正在运行中，PID: $PID"
            return 0
        else
            echo "$SERVICE_NAME 已崩溃，PID文件存在但进程不存在"
            return 1
        fi
    else
        echo "$SERVICE_NAME 未运行"
        return 3
    fi
}

restart() {
    stop
    sleep 2
    start
}

# 处理命令行参数
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        exit 1
esac

exit $? 