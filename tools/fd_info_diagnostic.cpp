#include <iostream>
#include <string>
#include <vector>
#include <librdkafka/rdkafkacpp.h>
#include "../src/kafka_config.h"
#include "../src/common.h"

// 简单的fd_info消费诊断工具
int main(int argc, char *argv[])
{
    if(argc < 2)
    {
        std::cout << "用法: " << argv[0] << " <kafka_brokers>" << std::endl;
        std::cout << "例如: " << argv[0] << " 119.145.39.253:9094" << std::endl;
        return 1;
    }

    std::string kafka_brokers = argv[1];
    std::cout << "开始诊断fd_info消费问题..." << std::endl;
    std::cout << "Kafka服务器: " << kafka_brokers << std::endl;
    // 创建消费者配置
    std::string errstr;
    RdKafka::Conf *conf = RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL);
    conf->set("bootstrap.servers", kafka_brokers, errstr);
    conf->set("group.id", "fd_info_diagnostic", errstr);
    conf->set("auto.offset.reset", "earliest", errstr);  // 从最早位置开始
    conf->set("enable.auto.commit", "false", errstr);
    // 创建消费者
    RdKafka::KafkaConsumer *consumer = RdKafka::KafkaConsumer::create(conf, errstr);

    if(!consumer)
    {
        std::cerr << "创建消费者失败: " << errstr << std::endl;
        delete conf;
        return 1;
    }

    std::cout << "消费者创建成功" << std::endl;
    // 手动分配所有分区
    std::vector<RdKafka::TopicPartition *> partitions;

    for(int32_t i = 0; i < PARTITION_COUNT; i++)
    {
        partitions.push_back(RdKafka::TopicPartition::create("fd_info", i));
    }

    RdKafka::ErrorCode err = consumer->assign(partitions);

    if(err != RdKafka::ERR_NO_ERROR)
    {
        std::cerr << "分配分区失败: " << RdKafka::err2str(err) << std::endl;

        for(auto *tp : partitions)
        {
            delete tp;
        }

        delete consumer;
        delete conf;
        return 1;
    }

    std::cout << "已分配 " << PARTITION_COUNT << " 个分区" << std::endl;

    // 清理分区对象
    for(auto *tp : partitions)
    {
        delete tp;
    }

    // 验证分区分配
    std::vector<RdKafka::TopicPartition *> assigned_partitions;
    RdKafka::ErrorCode assign_err = consumer->assignment(assigned_partitions);

    if(assign_err == RdKafka::ERR_NO_ERROR)
    {
        std::cout << "实际分配的分区数: " << assigned_partitions.size() << std::endl;

        for(auto *tp : assigned_partitions)
        {
            std::cout << "  分区: " << tp->topic() << "[" << tp->partition() << "]" << std::endl;
            delete tp;
        }
    }

    // 开始消费测试
    std::cout << "\n开始消费测试（60秒）..." << std::endl;
    int message_count = 0;
    time_t start_time = time(nullptr);

    while(time(nullptr) - start_time < 60)  // 测试60秒
    {
        RdKafka::Message *msg = consumer->consume(1000);  // 1秒超时

        if(msg)
        {
            switch(msg->err())
            {
                case RdKafka::ERR_NO_ERROR:
                    message_count++;
                    std::cout << "接收到消息 #" << message_count
                              << ", 分区: " << msg->partition()
                              << ", offset: " << msg->offset()
                              << ", 大小: " << msg->len() << " 字节" << std::endl;
                    break;

                case RdKafka::ERR__PARTITION_EOF:
                    // 分区末尾，正常
                    break;

                case RdKafka::ERR__TIMED_OUT:
                    // 超时，正常
                    std::cout << "." << std::flush;  // 显示进度
                    break;

                default:
                    std::cout << "消费错误: " << msg->errstr() << std::endl;
                    break;
            }

            delete msg;
        }
        else
        {
            std::cout << "?" << std::flush;  // 显示无消息状态
        }
    }

    std::cout << "\n\n诊断结果:" << std::endl;
    std::cout << "总共接收到 " << message_count << " 条fd_info消息" << std::endl;

    if(message_count == 0)
    {
        std::cout << "❌ 没有接收到任何fd_info消息！" << std::endl;
        std::cout << "可能的原因:" << std::endl;
        std::cout << "1. fd_info主题中没有新消息" << std::endl;
        std::cout << "2. 分区分配有问题" << std::endl;
        std::cout << "3. 消费者组配置有问题" << std::endl;
        std::cout << "4. 网络连接问题" << std::endl;
    }
    else
    {
        std::cout << "✅ fd_info消费正常" << std::endl;
    }

    // 清理资源
    consumer->close();
    delete consumer;
    delete conf;
    return 0;
}
