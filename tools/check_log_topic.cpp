#include <iostream>
#include <string>
#include <vector>
#include <librdkafka/rdkafkacpp.h>
#include "../src/kafka_config.h"
#include "../src/common.h"

// 检查特定日志文件主题的消息
int main(int argc, char *argv[])
{
    if(argc < 4)
    {
        std::cout << "用法: " << argv[0] << " <kafka_brokers> <chip_id> <partition>" << std::endl;
        std::cout << "例如: " << argv[0] << " **************:9094 002f07b4bffe90ce 72" << std::endl;
        return 1;
    }

    std::string kafka_brokers = argv[1];
    std::string chip_id = argv[2];
    int32_t partition = std::stoi(argv[3]);
    std::string topic = get_topic_for_chip_id(chip_id);
    std::cout << "检查日志文件主题消息..." << std::endl;
    std::cout << "Kafka服务器: " << kafka_brokers << std::endl;
    std::cout << "芯片ID: " << chip_id << std::endl;
    std::cout << "主题: " << topic << std::endl;
    std::cout << "分区: " << partition << std::endl;
    // 创建消费者配置
    std::string errstr;
    RdKafka::Conf *conf = RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL);
    conf->set("bootstrap.servers", kafka_brokers, errstr);
    conf->set("group.id", "log_topic_diagnostic", errstr);
    conf->set("auto.offset.reset", "earliest", errstr);
    conf->set("enable.auto.commit", "false", errstr);
    // 创建消费者
    RdKafka::KafkaConsumer *consumer = RdKafka::KafkaConsumer::create(conf, errstr);

    if(!consumer)
    {
        std::cerr << "创建消费者失败: " << errstr << std::endl;
        delete conf;
        return 1;
    }

    std::cout << "消费者创建成功" << std::endl;
    // 手动分配特定分区
    std::vector<RdKafka::TopicPartition *> partitions;
    partitions.push_back(RdKafka::TopicPartition::create(topic, partition));
    RdKafka::ErrorCode err = consumer->assign(partitions);

    if(err != RdKafka::ERR_NO_ERROR)
    {
        std::cerr << "分配分区失败: " << RdKafka::err2str(err) << std::endl;

        for(auto *tp : partitions)
        {
            delete tp;
        }

        delete consumer;
        delete conf;
        return 1;
    }

    std::cout << "已分配分区: " << topic << "[" << partition << "]" << std::endl;

    // 清理分区对象
    for(auto *tp : partitions)
    {
        delete tp;
    }

    // 获取分区的高水位和低水位
    std::vector<RdKafka::TopicPartition *> query_partitions;
    query_partitions.push_back(RdKafka::TopicPartition::create(topic, partition));
    int64_t low, high;
    RdKafka::ErrorCode watermark_err = consumer->query_watermark_offsets(topic, partition, &low, &high, 5000);

    if(watermark_err == RdKafka::ERR_NO_ERROR)
    {
        std::cout << "分区水位信息: 低水位=" << low << ", 高水位=" << high << ", 消息数=" <<
                  (high - low) << std::endl;
    }
    else
    {
        std::cout << "无法获取水位信息: " << RdKafka::err2str(watermark_err) << std::endl;
    }

    // 开始消费测试
    std::cout << "\n开始消费测试（30秒）..." << std::endl;
    int message_count = 0;
    time_t start_time = time(nullptr);

    while(time(nullptr) - start_time < 30)  // 测试30秒
    {
        RdKafka::Message *msg = consumer->consume(1000);  // 1秒超时

        if(msg)
        {
            switch(msg->err())
            {
                case RdKafka::ERR_NO_ERROR:
                    message_count++;
                    std::cout << "接收到消息 #" << message_count
                              << ", offset: " << msg->offset()
                              << ", 大小: " << msg->len() << " 字节";

                    // 尝试解析消息内容（前100字节）
                    if(msg->len() > 0)
                    {
                        std::cout << ", 内容预览: ";
                        const char *payload = static_cast<const char *>(msg->payload());

                        for(size_t i = 0; i < std::min((size_t)msg->len(), (size_t)50); i++)
                        {
                            if(payload[i] >= 32 && payload[i] <= 126)
                            {
                                std::cout << payload[i];
                            }
                            else
                            {
                                std::cout << ".";
                            }
                        }
                    }

                    std::cout << std::endl;
                    break;

                case RdKafka::ERR__PARTITION_EOF:
                    std::cout << "到达分区末尾" << std::endl;
                    break;

                case RdKafka::ERR__TIMED_OUT:
                    std::cout << "." << std::flush;
                    break;

                default:
                    std::cout << "消费错误: " << msg->errstr() << std::endl;
                    break;
            }

            delete msg;
        }
        else
        {
            std::cout << "?" << std::flush;
        }
    }

    std::cout << "\n\n诊断结果:" << std::endl;
    std::cout << "总共接收到 " << message_count << " 条日志文件消息" << std::endl;

    if(message_count == 0)
    {
        std::cout << "❌ 没有接收到任何日志文件消息！" << std::endl;
        std::cout << "可能的原因:" << std::endl;
        std::cout << "1. " << topic << "[" << partition << "] 中没有消息" << std::endl;
        std::cout << "2. 消费者组offset已经在最新位置" << std::endl;
        std::cout << "3. 分区分配有问题" << std::endl;
    }
    else
    {
        std::cout << "✅ 日志文件主题中有消息" << std::endl;
    }

    // 清理资源
    consumer->close();
    delete consumer;
    delete conf;
    return 0;
}
