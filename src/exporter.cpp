#include "exporter.h"
#include "kafka_config.h"
#include "fixed_thread_pool.h"

#include <iostream>
#include <fstream>
#include <sstream>
#include <csignal>
#include <getopt.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/resource.h>
#include <sys/time.h>
#include <unistd.h>
#include <fcntl.h>
#include <cstring>
#include <ctime>
#include <functional>
#include <map>
#include <vector>
#include <thread>
#include <dirent.h>
#include <algorithm>

// 全局变量
static std::atomic<bool> running(true);
static std::atomic<bool> force_consumer_reset(false);
AppConfig app_config; // 移除static，使其可以被其他源文件访问
static std::string log_file_path;
static LogLevel log_level = LOG_INFO;
static std::mutex log_mutex;
static std::ofstream log_file;
static time_t program_start_time;
static FixedThreadPool *thread_pool = nullptr;
static std::string device_info_csv_path = "../output/exporter/device_info.csv";
static std::mutex csv_mutex; // 保护CSV文件写入的互斥锁

// 日志轮转相关变量
static time_t log_file_open_time = 0;  // 当前日志文件的打开时间
static std::string current_log_date;   // 当前日志文件的日期
static size_t log_file_size = 0;       // 当前日志文件的大小

// MySQL连接池
#include "mysql_pool.h"
// 不再需要全局MySQL连接和互斥锁

// ANSI颜色代码定义
const char *LOG_COLOR_RESET = "\033[0m";
const char *LOG_COLOR_RED = "\033[31m";     // 错误 - 红色
const char *LOG_COLOR_YELLOW = "\033[33m";  // 警告 - 黄色
const char *LOG_COLOR_GREEN = "\033[32m";   // 信息 - 绿色
const char *LOG_COLOR_CYAN = "\033[36m";    // 调试 - 青色
const char *LOG_COLOR_BLUE = "\033[34m";    // 跟踪 - 蓝色

// 获取日志级别的字符串表示
const char *get_log_level_str(LogLevel level)
{
    switch(level)
    {
        case LOG_ERROR:
            return "错误";

        case LOG_WARN:
            return "警告";

        case LOG_INFO:
            return "信息";

        case LOG_DEBUG:
            return "调试";

        case LOG_TRACE:
            return "跟踪";

        default:
            return "未知";
    }
}

// 获取日志级别对应的颜色代码
const char *get_log_level_color(LogLevel level)
{
    switch(level)
    {
        case LOG_ERROR:
            return LOG_COLOR_RED;

        case LOG_WARN:
            return LOG_COLOR_YELLOW;

        case LOG_INFO:
            return LOG_COLOR_GREEN;

        case LOG_DEBUG:
            return LOG_COLOR_CYAN;

        case LOG_TRACE:
            return LOG_COLOR_BLUE;

        default:
            return LOG_COLOR_RESET;
    }
}

// 获取当前日期字符串
std::string get_current_date_str()
{
    time_t now = time(nullptr);
    struct tm *timeinfo = localtime(&now);
    char date_buffer[11];
    strftime(date_buffer, sizeof(date_buffer), "%Y-%m-%d", timeinfo);
    return std::string(date_buffer);
}

// 检查是否需要轮转日志文件
bool check_log_rotation()
{
    if(!app_config.log_rotate_enable)
    {
        return false;
    }

    // 按日期轮转
    if(app_config.log_rotate_mode == 0)
    {
        std::string today = get_current_date_str();

        if(current_log_date.empty())
        {
            current_log_date = today;
            return false;
        }

        // 如果日期变化，需要轮转
        return (today != current_log_date);
    }
    // 按大小轮转
    else if(app_config.log_rotate_mode == 1)
    {
        // 检查文件大小是否超过限制
        size_t max_size = static_cast<size_t>(app_config.log_rotate_size) * 1024 * 1024; // 转换为字节
        return (log_file_size > max_size);
    }

    return false;
}

// 轮转日志文件
bool rotate_log_file()
{
    std::lock_guard<std::mutex> lock(log_mutex);

    // 关闭当前日志文件
    if(log_file.is_open())
    {
        log_file.close();
    }

    // 生成新的日志文件名
    std::string base_log_path = app_config.log_file;
    std::string new_log_path;

    // 按日期轮转
    if(app_config.log_rotate_mode == 0)
    {
        // 获取当前日期
        current_log_date = get_current_date_str();
        // 构建新的日志文件名：原文件名.YYYY-MM-DD
        new_log_path = base_log_path + "." + current_log_date;
    }
    // 按大小轮转
    else
    {
        // 获取当前时间戳
        time_t now = time(nullptr);
        struct tm *timeinfo = localtime(&now);
        char timestamp[20];
        strftime(timestamp, sizeof(timestamp), "%Y%m%d-%H%M%S", timeinfo);
        // 构建新的日志文件名：原文件名.YYYYMMDD-HHMMSS
        new_log_path = base_log_path + "." + std::string(timestamp);
    }

    // 重命名当前日志文件
    if(rename(base_log_path.c_str(), new_log_path.c_str()) != 0)
    {
        // 如果重命名失败，尝试复制文件内容
        std::ifstream src(base_log_path, std::ios::binary);
        std::ofstream dst(new_log_path, std::ios::binary);

        if(src && dst)
        {
            dst << src.rdbuf();
            src.close();
            dst.close();
            // 清空原文件
            std::ofstream clear(base_log_path, std::ios::trunc);
            clear.close();
        }
        else
        {
            // 如果复制也失败，则继续使用原文件
            std::cerr << "无法轮转日志文件: " << strerror(errno) << std::endl;
            // 重新打开原文件
            log_file.open(base_log_path, std::ios::app);
            return false;
        }
    }

    // 清理旧的日志文件
    if(app_config.log_rotate_count > 0)
    {
        // 获取日志目录
        std::string log_dir = base_log_path.substr(0, base_log_path.find_last_of('/'));
        std::string log_base_name = base_log_path.substr(base_log_path.find_last_of('/') + 1);
        // 列出目录中的所有文件
        DIR *dir = opendir(log_dir.c_str());

        if(dir)
        {
            std::vector<std::string> log_files;
            struct dirent *entry;

            while((entry = readdir(dir)) != nullptr)
            {
                std::string filename = entry->d_name;

                // 检查是否是轮转的日志文件
                if(filename.find(log_base_name + ".") == 0)
                {
                    log_files.push_back(log_dir + "/" + filename);
                }
            }

            closedir(dir);
            // 按修改时间排序
            std::sort(log_files.begin(), log_files.end(), [](const std::string & a, const std::string & b)
            {
                struct stat stat_a, stat_b;
                stat(a.c_str(), &stat_a);
                stat(b.c_str(), &stat_b);
                return stat_a.st_mtime > stat_b.st_mtime;
            });

            // 删除超出保留数量的旧文件
            if(log_files.size() > static_cast<size_t>(app_config.log_rotate_count))
            {
                for(size_t i = app_config.log_rotate_count; i < log_files.size(); i++)
                {
                    unlink(log_files[i].c_str());
                }
            }
        }
    }

    // 打开新的日志文件
    log_file.open(base_log_path, std::ios::app);

    if(!log_file.is_open())
    {
        std::cerr << "无法打开新的日志文件: " << base_log_path << std::endl;
        return false;
    }

    // 重置日志文件大小和打开时间
    log_file_size = 0;
    log_file_open_time = time(nullptr);
    return true;
}

// 实现日志记录函数
void log_message(LogLevel level, const std::string &message)
{
    if(level > log_level)
    {
        return;
    }

    // 获取当前时间
    time_t now = time(nullptr);
    struct tm *timeinfo = localtime(&now);
    char time_buffer[20];
    strftime(time_buffer, sizeof(time_buffer), "%Y-%m-%d %H:%M:%S", timeinfo);
    std::string formatted_message = std::string(time_buffer) + " [" + get_log_level_str(level) + "] " + message;
    // 检查是否需要轮转日志文件
    bool need_rotation = check_log_rotation();
    // 写入日志文件
    {
        std::lock_guard<std::mutex> lock(log_mutex);

        // 如果需要轮转日志文件
        if(need_rotation)
        {
            rotate_log_file();
        }

        if(log_file.is_open())
        {
            log_file << formatted_message << std::endl;
            log_file.flush();
            // 更新日志文件大小
            log_file_size += formatted_message.length() + 1; // +1 for newline
        }
    }

    // 非守护进程模式下，总是输出到终端
    // 守护进程模式下，只有当详细度足够时才输出
    if(!app_config.run_as_daemon || app_config.verbose_level >= level)
    {
        std::cout << get_log_level_color(level) << formatted_message << LOG_COLOR_RESET << std::endl;
    }
}

// 确保目录存在
bool ensure_dir_exists(const std::string &dir_path)
{
    if(access(dir_path.c_str(), F_OK) == 0)
    {
        return true;
    }

    // 创建多级目录
    size_t pos = 0;
    std::string path = dir_path;
    // 替换Windows风格的路径分隔符
    std::replace(path.begin(), path.end(), '\\', '/');

    // 确保路径以'/'结尾
    if(path.back() != '/')
    {
        path += '/';
    }

    while((pos = path.find('/', pos + 1)) != std::string::npos)
    {
        std::string sub_path = path.substr(0, pos);

        if(!sub_path.empty() && access(sub_path.c_str(), F_OK) != 0)
        {
            if(mkdir(sub_path.c_str(), 0755) != 0 && errno != EEXIST)
            {
                log_message(LOG_ERROR, "无法创建目录: " + sub_path + ", 错误: " + strerror(errno));
                return false;
            }
        }
    }

    return true;
}

// 信号处理函数
void signal_handler(int sig)
{
    if(sig == SIGINT || sig == SIGTERM)
    {
        static int signal_count = 0;
        signal_count++;

        if(signal_count == 1)
        {
            log_message(LOG_WARN, "接收到终止信号(" + std::to_string(sig) + ")，将在下载完成后关闭...");
            // 设置标志，允许当前下载完成后再关闭
            running = false;

            // 立即保存状态
            if(thread_pool != nullptr)
            {
                log_message(LOG_INFO, "开始关闭固定线程池...");
                thread_pool->shutdown();
                log_message(LOG_INFO, "固定线程池已关闭");
            }
        }
        else if(signal_count >= 3)
        {
            log_message(LOG_WARN, "强制退出程序");
            exit(1); // 强制退出
        }
        else
        {
            log_message(LOG_WARN, "再次接收到终止信号，即将关闭...");
        }
    }
    else if(sig == SIGUSR1)
    {
        // 用户信号1用于打印状态
        log_message(LOG_INFO, "接收到SIGUSR1信号，打印状态信息");

        if(thread_pool != nullptr)
        {
            thread_pool->printStatusReport();
        }
    }
    else if(sig == SIGUSR2)
    {
        // 用户信号2用于打印线程状态报告
        log_message(LOG_INFO, "收到SIGUSR2信号，打印详细状态报告...");

        if(thread_pool != nullptr)
        {
            thread_pool->printStatusReport();

            // 打印健康状态
            if(thread_pool->isHealthy())
            {
                log_message(LOG_INFO, "线程池健康状态: 正常");
            }
            else
            {
                log_message(LOG_WARN, "线程池健康状态: 异常");
            }
        }
    }
}

// 初始化守护进程
bool daemonize()
{
    // 创建子进程
    pid_t pid = fork();

    if(pid < 0)
    {
        log_message(LOG_ERROR, "无法创建子进程: " + std::string(strerror(errno)));
        return false;
    }

    // 父进程退出
    if(pid > 0)
    {
        exit(0);
    }

    // 创建新会话
    if(setsid() < 0)
    {
        log_message(LOG_ERROR, "无法创建新会话: " + std::string(strerror(errno)));
        return false;
    }

    // 忽略SIGHUP信号
    signal(SIGHUP, SIG_IGN);
    // 创建第二个子进程
    pid = fork();

    if(pid < 0)
    {
        log_message(LOG_ERROR, "无法创建第二个子进程: " + std::string(strerror(errno)));
        return false;
    }

    // 第一个子进程退出
    if(pid > 0)
    {
        exit(0);
    }

    // 更改工作目录
    if(chdir("/") < 0)
    {
        log_message(LOG_ERROR, "无法更改工作目录: " + std::string(strerror(errno)));
        return false;
    }

    // 重定向标准输入/输出/错误到/dev/null
    int fd = open("/dev/null", O_RDWR);

    if(fd < 0)
    {
        log_message(LOG_ERROR, "无法打开/dev/null: " + std::string(strerror(errno)));
        return false;
    }

    dup2(fd, STDIN_FILENO);
    dup2(fd, STDOUT_FILENO);
    dup2(fd, STDERR_FILENO);

    if(fd > STDERR_FILENO)
    {
        close(fd);
    }

    // 创建PID文件
    if(!app_config.pid_file.empty())
    {
        std::ofstream pid_file(app_config.pid_file);

        if(pid_file.is_open())
        {
            pid_file << getpid();
            pid_file.close();
        }
        else
        {
            log_message(LOG_ERROR, "无法创建PID文件: " + app_config.pid_file);
            return false;
        }
    }

    return true;
}

// 解析命令行参数
AppConfig parse_command_line(int argc, char **argv)
{
    AppConfig config;
    static struct option long_options[] =
    {
        {"verbose",        required_argument, 0, 'v'},
        {"output-dir",     required_argument, 0, 'o'},
        {"auto-fill",      no_argument,       0, 'a'},
        {"no-sort",        no_argument,       0, 'n'},
        {"state-file",     required_argument, 0, 's'},
        {"log-file",       required_argument, 0, 'l'},
        {"log-level",      required_argument, 0, 'L'},
        {"daemon",         no_argument,       0, 'd'},
        {"pid-file",       required_argument, 0, 'p'},
        {"kafka-brokers",  required_argument, 0, 'k'},
        // max-tasks 已弃用，现在使用固定线程池
        {"retry-count",    required_argument, 0, 'r'},
        {"retry-delay",    required_argument, 0, 'R'},
        {"device-info-csv", required_argument, 0, 'i'},
        {"use-mysql",      no_argument,       0, 'M'},
        {"no-mysql",       no_argument,       0, 'N'},
        {"mysql-host",     required_argument, 0, 'H'},
        {"mysql-port",     required_argument, 0, 'P'},
        {"mysql-user",     required_argument, 0, 'u'},
        {"mysql-pass",     required_argument, 0, 'w'},
        {"mysql-db",       required_argument, 0, 'D'},
        {"log-rotate",     no_argument,       0, 'x'},
        {"log-rotate-mode", required_argument, 0, 'X'},
        {"log-rotate-size", required_argument, 0, 'S'},
        {"log-rotate-count", required_argument, 0, 'C'},
        {"ignore-checksum", no_argument,       0, 'I'},
        {"checksum-threshold", required_argument, 0, 'T'},
        {"consume-mode",   required_argument, 0, 'c'},
        {"help",           no_argument,       0, 'h'},
        {0, 0, 0, 0}
    };
    int option_index = 0;
    int c;

    while((c = getopt_long(argc, argv, "v:o:ans:l:L:dp:k:m:r:R:i:MNH:P:u:w:D:xX:S:C:IT:c:h", long_options,
                           &option_index)) != -1)
    {
        switch(c)
        {
            case 'v':
                config.verbose_level = std::stoi(optarg);
                break;

            case 'o':
                config.output_dir = optarg;
                break;

            case 'a':
                config.auto_fill = true;
                break;

            case 'n':
                config.sort_messages = false;
                break;

            case 's':
                config.state_file = optarg;
                break;

            case 'l':
                config.log_file = optarg;
                break;

            case 'L':
                config.log_level = std::stoi(optarg);
                break;

            case 'd':
                config.run_as_daemon = true;
                break;

            case 'p':
                config.pid_file = optarg;
                break;

            case 'k':
                config.kafka_brokers = optarg;
                break;

            // case 'm': max-tasks 已弃用，现在使用固定线程池

            case 'r':
                config.retry_count = std::stoi(optarg);
                break;

            case 'R':
                config.retry_delay = std::stoi(optarg);
                break;

            case 'i':
                config.device_info_csv = optarg;
                break;

            case 'M':
                config.use_mysql = true;
                break;

            case 'N':
                config.use_mysql = false;
                break;

            case 'H':
                config.mysql_host = optarg;
                break;

            case 'P':
                config.mysql_port = std::stoi(optarg);
                break;

            case 'u':
                config.mysql_user = optarg;
                break;

            case 'w':
                config.mysql_pass = optarg;
                break;

            case 'D':
                config.mysql_db = optarg;
                break;

            case 'x':
                config.log_rotate_enable = true;
                break;

            case 'X':
                config.log_rotate_mode = std::stoi(optarg);
                break;

            case 'S':
                config.log_rotate_size = std::stoi(optarg);
                break;

            case 'C':
                config.log_rotate_count = std::stoi(optarg);
                break;

            case 'I':
                config.ignore_small_checksum_mismatch = true;
                break;

            case 'T':
                config.checksum_mismatch_threshold = std::stod(optarg);
                break;

            case 'c':
                config.consume_mode = optarg;
                break;

            case 'h':
                print_usage(argv[0]);
                exit(0);
                break;

            case '?':
                // getopt_long已经输出错误信息
                exit(1);
                break;

            default:
                std::cerr << "未知选项: " << (char)c << std::endl;
                exit(1);
        }
    }

    return config;
}

// 打印使用说明
void print_usage(const char *program_name)
{
    std::cout << "使用方法: " << program_name << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -v, --verbose=LEVEL       详细程度 (0-4, 默认: 0)" << std::endl;
    std::cout << "  -o, --output-dir=DIR      输出目录 (默认: ../output/exporter)" << std::endl;
    std::cout << "  -a, --auto-fill           自动填充缺失帧 (默认: 禁用)" << std::endl;
    std::cout << "  -n, --no-sort             不排序消息 (默认: 启用排序)" << std::endl;
    std::cout << "  -s, --state-file=FILE     状态文件 (默认: " << EXP_STATE_FILE << ")" << std::endl;
    std::cout << "  -l, --log-file=FILE       日志文件 (默认: " << EXP_LOG_DIR << "/exporter.log)" << std::endl;
    std::cout << "  -L, --log-level=LEVEL     日志级别 (0-4, 默认: 2)" << std::endl;
    std::cout << "  -d, --daemon              作为守护进程运行 (默认: 禁用)" << std::endl;
    std::cout << "  -p, --pid-file=FILE       PID文件 (默认: ../output/exporter.pid)" << std::endl;
    std::cout << "  -k, --kafka-brokers=LIST  Kafka服务器地址 (默认: " << KAFKA_BROKERS << ")" << std::endl;
    // max-tasks 已弃用，现在使用固定线程池 (32主题 × 200分区 = 6400线程)
    std::cout << "  -r, --retry-count=NUM     重试次数 (默认: " << EXP_DEFAULT_RETRY_COUNT << ")" << std::endl;
    std::cout << "  -R, --retry-delay=MS      重试延迟(毫秒) (默认: " << EXP_DEFAULT_RETRY_DELAY << ")" <<
              std::endl;
    std::cout << "  -i, --device-info-csv=FILE 设备信息CSV文件路径 (默认: ../output/exporter/device_info.csv)" <<
              std::endl;
    std::cout << "  -M, --use-mysql           启用MySQL功能 (默认: 启用)" << std::endl;
    std::cout << "  -N, --no-mysql            禁用MySQL功能" << std::endl;
    std::cout << "  -H, --mysql-host=HOST     MySQL服务器地址 (默认: " << DEFAULT_MYSQL_HOST << ")" << std::endl;
    std::cout << "  -P, --mysql-port=PORT     MySQL服务器端口 (默认: " << DEFAULT_MYSQL_PORT << ")" << std::endl;
    std::cout << "  -u, --mysql-user=USER     MySQL用户名 (默认: " << DEFAULT_MYSQL_USER << ")" << std::endl;
    std::cout << "  -w, --mysql-pass=PASS     MySQL密码 (默认: " << DEFAULT_MYSQL_PASS << ")" << std::endl;
    std::cout << "  -D, --mysql-db=DB         MySQL数据库名 (默认: " << DEFAULT_MYSQL_DB << ")" << std::endl;
    std::cout << "  -x, --log-rotate          启用日志轮转 (默认: 启用)" << std::endl;
    std::cout << "  -X, --log-rotate-mode=MODE 日志轮转模式 (0=按日期, 1=按大小, 默认: 0)" << std::endl;
    std::cout << "  -S, --log-rotate-size=SIZE 日志轮转大小(MB) (默认: 100)" << std::endl;
    std::cout << "  -C, --log-rotate-count=NUM 保留的日志文件数量 (默认: 10)" << std::endl;
    std::cout << "  -I, --ignore-checksum     忽略小的校验和不匹配 (默认: 禁用)" << std::endl;
    std::cout << "  -T, --checksum-threshold=PCT 校验和不匹配的阈值(百分比) (默认: 0.0，严格校验)" <<
              std::endl;
    std::cout << "  -c, --consume-mode=MODE   消费模式 (latest/earliest/stored, 默认: latest)" << std::endl;
    std::cout << "  -h, --help                显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "日志级别: 0=错误, 1=警告, 2=信息, 3=调试, 4=跟踪" << std::endl;
    std::cout << "详细程度: 0=静默, 1=基本, 2=详细, 3=非常详细, 4=调试" << std::endl;
    std::cout << std::endl;
    std::cout << "信号控制:" << std::endl;
    std::cout << "  SIGUSR1                   保存当前状态" << std::endl;
    std::cout << "  SIGUSR2                   强制重置僵尸线程、重置消费者并打印状态报告" << std::endl;
    std::cout << "  SIGINT/SIGTERM            优雅关闭程序" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  # 手动触发线程状态报告" << std::endl;
    std::cout << "  kill -SIGUSR2 $(cat /path/to/exporter.pid)" << std::endl;
    std::cout << "  # 保存当前状态" << std::endl;
    std::cout << "  kill -SIGUSR1 $(cat /path/to/exporter.pid)" << std::endl;
}

// 初始化
bool initialize()
{
    // 确保日志目录存在
    std::string log_dir = app_config.log_file.substr(0, app_config.log_file.find_last_of('/'));

    if(!ensure_dir_exists(log_dir))
    {
        std::cerr << "无法创建日志目录: " << log_dir << std::endl;
        return false;
    }

    // 打开日志文件
    log_file.open(app_config.log_file, std::ios::app);

    if(!log_file.is_open())
    {
        std::cerr << "无法打开日志文件: " << app_config.log_file << std::endl;
        return false;
    }

    // 设置日志级别
    log_level = static_cast<LogLevel>(app_config.log_level);
    log_file_path = app_config.log_file;
    // 初始化日志轮转相关变量
    log_file_open_time = time(nullptr);
    current_log_date = get_current_date_str();
    // 获取当前日志文件大小
    struct stat file_stat;

    if(stat(app_config.log_file.c_str(), &file_stat) == 0)
    {
        log_file_size = file_stat.st_size;
    }
    else
    {
        log_file_size = 0;
    }

    // 设置设备信息CSV路径
    device_info_csv_path = app_config.device_info_csv;
    // 确保设备信息CSV目录存在
    std::string csv_dir = device_info_csv_path.substr(0, device_info_csv_path.find_last_of('/'));

    if(!ensure_dir_exists(csv_dir))
    {
        log_message(LOG_ERROR, "无法创建设备信息CSV目录: " + csv_dir);
        return false;
    }

    // 如果启用了MySQL，初始化连接
    if(app_config.use_mysql)
    {
        if(!init_mysql_connection())
        {
            log_message(LOG_ERROR, "MySQL初始化失败，但程序将继续使用CSV文件");
            // 不因MySQL连接失败而停止程序
        }
    }

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);

    // 确保输出目录存在
    if(!ensure_dir_exists(app_config.output_dir))
    {
        log_message(LOG_ERROR, "无法创建输出目录: " + app_config.output_dir);
        return false;
    }

    // 确保状态文件目录存在
    std::string state_dir = app_config.state_file.substr(0, app_config.state_file.find_last_of('/'));

    if(!ensure_dir_exists(state_dir))
    {
        log_message(LOG_ERROR, "无法创建状态文件目录: " + state_dir);
        return false;
    }

    return true;
}

// 清理资源
void cleanup()
{
    if(thread_pool != nullptr)
    {
        thread_pool->shutdown();
        delete thread_pool;
        thread_pool = nullptr;
    }

    // 关闭MySQL连接
    close_mysql_connection();

    // 关闭日志文件
    if(log_file.is_open())
    {
        log_file.close();
    }

    // 如果是作为守护进程运行，删除PID文件
    if(app_config.run_as_daemon && !app_config.pid_file.empty())
    {
        unlink(app_config.pid_file.c_str());
    }
}

// 创建设备信息消费者
RdKafka::KafkaConsumer *create_device_info_consumer()
{
    std::string errstr;
    RdKafka::Conf *conf = RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL);
    // 设置基本Kafka配置
    conf->set("bootstrap.servers", app_config.kafka_brokers, errstr);
    // 根据消费模式选择消费者组策略
    std::string group_id;

    if(app_config.consume_mode == "stored")
    {
        group_id = std::string(CONSUMER_GROUP_ID) + "_stable"; // 固定消费者组，支持断点续传
    }
    else
    {
        group_id = std::string(CONSUMER_GROUP_ID) + "_" + app_config.consume_mode + "_" + std::to_string(time(
                                   nullptr)); // 唯一消费者组
    }

    conf->set("group.id", group_id.c_str(), errstr);
    log_message(LOG_INFO, "使用消费者组: " + group_id + " (模式: " + app_config.consume_mode + ")");
    // 设置offset重置策略：stored模式或earliest模式都从最早开始，latest从最新开始
    std::string offset_reset = (app_config.consume_mode == "stored"
                                || app_config.consume_mode == "earliest") ? "earliest" : app_config.consume_mode;
    conf->set("auto.offset.reset", offset_reset, errstr);
    conf->set("enable.auto.commit", "false", errstr);  // 禁用自动提交，确保数据完整性
    conf->set("enable.auto.offset.store", "false", errstr); // 禁用自动offset存储
    // 设置关键的超时和心跳配置，优化消费稳定性
    std::vector<std::pair<std::string, std::string>> critical_configs =
    {
        {"session.timeout.ms", "300000"},        // 5分钟会话超时，更宽松
        {"heartbeat.interval.ms", "30000"},      // 30秒心跳间隔，减少网络负载
        {"max.poll.interval.ms", "1800000"},     // 30分钟最大轮询间隔，避免被踢出
        {"fetch.wait.max.ms", "5000"},           // 5秒等待时间，减少空轮询
        {"fetch.min.bytes", "1"},                // 最小获取字节数
        {"fetch.wait.max.ms", "5000"},           // 最大等待时间
        {"fetch.error.backoff.ms", "1000"},      // 错误回退时间
        {"reconnect.backoff.ms", "1000"},        // 1秒重连回退时间
        {"reconnect.backoff.max.ms", "10000"},   // 10秒最大重连回退时间
        {"request.timeout.ms", "60000"},         // 1分钟请求超时
        {"socket.timeout.ms", "120000"},         // 2分钟Socket超时
        {"coordinator.query.interval.ms", "60000"}, // 1分钟协调器查询间隔
        {"connections.max.idle.ms", "540000"},   // 9分钟连接空闲超时
        {"retry.backoff.ms", "1000"}             // 重试回退时间
    };

    for(const auto &cfg : critical_configs)
    {
        if(conf->set(cfg.first, cfg.second, errstr) != RdKafka::Conf::CONF_OK)
        {
            log_message(LOG_ERROR, "设置设备信息消费者配置" + cfg.first + "失败: " + errstr);
        }
        else
        {
            log_message(LOG_DEBUG, "设置设备信息消费者配置: " + cfg.first + "=" + cfg.second);
        }
    }

    // 创建消费者
    RdKafka::KafkaConsumer *consumer = RdKafka::KafkaConsumer::create(conf, errstr);

    if(!consumer)
    {
        log_message(LOG_ERROR, "无法创建Kafka消费者: " + errstr);
        delete conf;
        return nullptr;
    }

    // 手动分配所有分区，确保能接收到所有芯片ID的消息
    // 因为生产者会根据芯片ID计算特定分区发送消息
    std::vector<RdKafka::TopicPartition *> partitions;

    for(int32_t i = 0; i < PARTITION_COUNT; i++)
    {
        partitions.push_back(RdKafka::TopicPartition::create("fd_info", i));
    }

    RdKafka::ErrorCode err = consumer->assign(partitions);

    if(err != RdKafka::ERR_NO_ERROR)
    {
        log_message(LOG_ERROR, "无法分配fd_info主题分区: " + RdKafka::err2str(err));

        // 清理分区对象
        for(auto *tp : partitions)
        {
            delete tp;
        }

        delete consumer;
        return nullptr;
    }

    // 验证分区分配是否成功
    std::vector<RdKafka::TopicPartition *> assigned_partitions;
    RdKafka::ErrorCode assign_err = consumer->assignment(assigned_partitions);

    if(assign_err == RdKafka::ERR_NO_ERROR)
    {
        log_message(LOG_INFO, "已成功分配fd_info主题的 " + std::to_string(assigned_partitions.size()) +
                    " 个分区 (期望: " + std::to_string(PARTITION_COUNT) + ")");

        // 清理验证用的分区对象
        for(auto *tp : assigned_partitions)
        {
            delete tp;
        }

        if(assigned_partitions.size() != PARTITION_COUNT)
        {
            log_message(LOG_WARN, "分配的分区数量不匹配，可能影响消息接收");
        }
    }
    else
    {
        log_message(LOG_WARN, "无法验证分区分配状态: " + RdKafka::err2str(assign_err));
    }

    // 清理分区对象
    for(auto *tp : partitions)
    {
        delete tp;
    }

    // 最终验证：尝试立即消费一次，检查消费者是否真正工作
    log_message(LOG_INFO, "验证fd_info消费者是否正常工作...");
    RdKafka::Message *test_msg = consumer->consume(100); // 100ms超时测试

    if(test_msg)
    {
        if(test_msg->err() == RdKafka::ERR_NO_ERROR)
        {
            log_message(LOG_INFO, "fd_info消费者验证成功：立即接收到消息");
        }
        else if(test_msg->err() == RdKafka::ERR__TIMED_OUT || test_msg->err() == RdKafka::ERR__PARTITION_EOF)
        {
            log_message(LOG_INFO, "fd_info消费者验证成功：消费者正常工作（当前无新消息）");
        }
        else
        {
            log_message(LOG_WARN, "fd_info消费者验证警告：" + test_msg->errstr());
        }

        delete test_msg;
    }
    else
    {
        log_message(LOG_WARN, "fd_info消费者验证：无法获取测试消息");
    }

    return consumer;
}

// save_state 和 load_state 函数已被FixedThreadPool替代，不再需要

// 将设备信息导出到CSV表格
bool export_device_info_to_csv(const binary_stream::DeviceInfo &device_info)
{
    // 获取互斥锁保护文件写入
    std::lock_guard<std::mutex> lock(csv_mutex);
    bool is_new_file = access(device_info_csv_path.c_str(), F_OK) != 0;
    // 以追加模式打开CSV文件
    std::ofstream csv_file(device_info_csv_path, std::ios::app);

    if(!csv_file.is_open())
    {
        log_message(LOG_ERROR, "无法打开CSV文件: " + device_info_csv_path);
        return false;
    }

    // 如果是新文件，写入CSV标题行
    if(is_new_file)
    {
        csv_file <<
                 "接收时间,芯片ID,日期时间,日志文件名,文件大小(字节),总内存(KB),可用内存(KB),温度(℃),硬件版本,系统版本,软件版本,机器型号,校验和"
                 << std::endl;
    }

    // 获取当前接收时间
    std::time_t now = std::time(nullptr);
    char time_buf[30];
    std::strftime(time_buf, sizeof(time_buf), "%Y-%m-%d %H:%M:%S", std::localtime(&now));
    // 写入CSV数据行
    csv_file << time_buf << ","
             << device_info.chip_id() << ","
             << device_info.datetime() << ","
             << device_info.log_file_name() << ","
             << device_info.log_file_size() << ","
             << device_info.total_memory() << ","
             << device_info.free_memory() << ","
             << device_info.temperature() << ","
             << device_info.hardware_version() << ","
             << device_info.system_version() << ","
             << device_info.software_version() << ","
             << device_info.machine_model() << ","
             << device_info.file_checksum() << std::endl;
    // 刷新并关闭文件
    csv_file.flush();
    csv_file.close();
    log_message(LOG_DEBUG, "已将设备信息(" + device_info.chip_id() + ")更新到表格");
    return true;
}

// 函数前向声明
bool create_device_info_table();

// 将设备信息保存到MySQL数据库
bool save_device_info_to_mysql(const binary_stream::DeviceInfo &device_info)
{
    if(!app_config.use_mysql)
    {
        return true; // MySQL功能未启用，直接返回成功
    }

    // 检查连接池是否初始化
    auto &pool = MySQLConnectionPool::getInstance();

    if(!pool.isInitialized())
    {
        log_message(LOG_WARN, "MySQL连接池未初始化，尝试初始化");

        if(!init_mysql_connection())
        {
            log_message(LOG_ERROR, "初始化MySQL连接池失败，跳过保存设备信息");
            return false;
        }
    }

    // 获取当前接收时间
    std::time_t now = std::time(nullptr);
    char time_buf[30];
    std::strftime(time_buf, sizeof(time_buf), "%Y-%m-%d %H:%M:%S", std::localtime(&now));
    // 准备SQL语句 - 使用参数化查询
    std::stringstream sql;
    sql << "INSERT INTO device_info ("
        << "receive_time, chip_id, datetime, log_file_name, log_file_size, "
        << "total_memory, free_memory, temperature, hardware_version, "
        << "system_version, software_version, machine_model, file_checksum, "
        << "process_status"
        << ") VALUES ('"
        << time_buf << "', '"
        << device_info.chip_id() << "', '"
        << device_info.datetime() << "', '"
        << device_info.log_file_name() << "', "
        << device_info.log_file_size() << ", "
        << device_info.total_memory() << ", "
        << device_info.free_memory() << ", "
        << device_info.temperature() << ", '"
        << device_info.hardware_version() << "', '"
        << device_info.system_version() << "', '"
        << device_info.software_version() << "', '"
        << device_info.machine_model() << "', '"
        << device_info.file_checksum() << "', "
        << "0)"; // 初始状态为0，表示待处理
    // 获取SQL语句
    std::string sql_str = sql.str();
    // 使用原子变量跟踪操作是否完成
    std::atomic<bool> operation_completed(false);
    // 添加调试日志
    log_message(LOG_DEBUG, "尝试保存设备信息: chip_id=" + device_info.chip_id() + ", datetime=" +
                device_info.datetime());
    // 定义重试参数
    const int MAX_RETRIES = 3;
    std::atomic<int> retry_count(0);
    std::atomic<bool> insert_success(false);
    // 定义重试函数
    std::function<void(const std::string &)> retry_insert = [&](const std::string & sql)
    {
        // 如果已经成功或达到最大重试次数，不再重试
        if(insert_success || retry_count >= MAX_RETRIES)
        {
            operation_completed = true;
            return;
        }

        retry_count++;
        log_message(LOG_INFO, "尝试保存设备信息 (尝试 " + std::to_string(retry_count) + "/" +
                    std::to_string(MAX_RETRIES) + "): chip_id=" + device_info.chip_id() +
                    ", datetime=" + device_info.datetime());
        // 异步执行SQL
        pool.executeAsync(sql, [chip_id = device_info.chip_id(), datetime = device_info.datetime(),
                                        &operation_completed, &insert_success, &retry_count, &retry_insert, sql, MAX_RETRIES]
                          (bool success, const std::string & error)
        {
            if(!success)
            {
                log_message(LOG_ERROR, "保存设备信息到MySQL失败 (尝试 " + std::to_string(retry_count) +
                            "/" + std::to_string(MAX_RETRIES) + "): " + error);

                // 如果是可重试的错误，进行重试
                if(error.find("Lost connection") != std::string::npos ||
                        error.find("server has gone away") != std::string::npos ||
                        error.find("Commands out of sync") != std::string::npos ||
                        error.find("Can't connect") != std::string::npos)
                {
                    // 等待一小段时间后重试
                    std::this_thread::sleep_for(std::chrono::milliseconds(500 * retry_count));
                    retry_insert(sql);
                    return;
                }

                // 不可重试的错误，标记完成
                operation_completed = true;
                return;
            }

            // 插入成功，进行验证
            insert_success = true;
            log_message(LOG_DEBUG, "设备信息已插入数据库，正在验证: chip_id=" + chip_id +
                        ", datetime=" + datetime);
            // 使用executeAsync进行验证
            auto &pool = MySQLConnectionPool::getInstance();
            pool.executeAsync("SELECT COUNT(*) FROM device_info WHERE chip_id='" + chip_id +
                              "' AND datetime='" + datetime + "'",
                              [chip_id, datetime, &operation_completed]
                              (bool success, const std::string & error)
            {
                if(!success)
                {
                    log_message(LOG_WARN, "验证设备信息保存失败，但插入可能已成功: " + error);
                }
                else
                {
                    log_message(LOG_INFO, "已成功将设备信息保存到MySQL数据库: chip_id=" +
                                chip_id + ", datetime=" + datetime);
                }

                operation_completed = true;
            });
        });
    };
    // 开始第一次尝试
    retry_insert(sql_str);

    // 等待操作完成，但最多等待1秒
    for(int i = 0; i < 10; i++)
    {
        if(operation_completed)
        {
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // 即使操作尚未完成，也返回true
    // 因为操作是异步的，实际结果会在回调中处理
    return true;
}

// 更新设备信息处理状态
// 这个函数在task_manager.cpp中被声明为extern
bool update_device_info_status(const std::string &chip_id, const std::string &datetime,
                               int status, uint64_t processed_size,
                               const std::string &actual_checksum, const std::string &error_message)
{
    if(!app_config.use_mysql)
    {
        return true; // MySQL功能未启用，直接返回成功
    }

    // 检查连接池是否初始化
    auto &pool = MySQLConnectionPool::getInstance();

    if(!pool.isInitialized())
    {
        log_message(LOG_WARN, "MySQL连接池未初始化，尝试初始化");

        if(!init_mysql_connection())
        {
            log_message(LOG_ERROR, "初始化MySQL连接池失败，跳过更新设备信息状态");
            return false;
        }
    }

    // 获取当前处理时间
    std::time_t now = std::time(nullptr);
    char time_buf[30];
    std::strftime(time_buf, sizeof(time_buf), "%Y-%m-%d %H:%M:%S", std::localtime(&now));
    // 准备SQL语句
    std::stringstream sql;
    sql << "UPDATE device_info SET "
        << "process_status = " << status << ", "
        << "processed_time = '" << time_buf << "', "
        << "processed_size = " << processed_size << ", "
        << "actual_checksum = '" << actual_checksum << "', "
        << "error_message = '" << error_message << "' "
        << "WHERE chip_id = '" << chip_id << "' AND datetime = '" << datetime << "'";
    // 获取SQL语句
    std::string sql_str = sql.str();
    // 使用原子变量跟踪操作是否完成
    std::atomic<bool> operation_completed(false);
    std::atomic<bool> update_success(false);
    std::atomic<int> affected_rows(0);
    // 添加调试日志
    log_message(LOG_DEBUG, "尝试更新设备信息: chip_id=" + chip_id + ", datetime=" + datetime);
    // 定义重试参数
    const int MAX_RETRIES = 3;
    std::atomic<int> retry_count(0);
    std::atomic<bool> update_attempted(false);
    // 定义重试函数
    std::function<void()> retry_update = [&]()
    {
        // 如果已经尝试过更新或达到最大重试次数，不再重试
        if(update_attempted || retry_count >= MAX_RETRIES)
        {
            operation_completed = true;
            return;
        }

        retry_count++;
        log_message(LOG_INFO, "尝试更新设备信息状态 (尝试 " + std::to_string(retry_count) + "/" +
                    std::to_string(MAX_RETRIES) + "): chip_id=" + chip_id +
                    ", datetime=" + datetime);
        // 直接执行更新，不再检查记录是否存在
        pool.executeAsync(sql_str, [chip_id, datetime, status, &operation_completed,
                                             &update_success, &affected_rows, &retry_count,
                                             &retry_update, &update_attempted, MAX_RETRIES]
                          (bool success, const std::string & error)
        {
            // 标记已尝试更新
            update_attempted = true;

            if(!success)
            {
                log_message(LOG_ERROR, "更新设备信息状态失败 (尝试 " + std::to_string(retry_count) +
                            "/" + std::to_string(MAX_RETRIES) + "): " + error);

                // 如果是可重试的错误，进行重试
                if(error.find("Lost connection") != std::string::npos ||
                        error.find("server has gone away") != std::string::npos ||
                        error.find("Commands out of sync") != std::string::npos ||
                        error.find("Can't connect") != std::string::npos)
                {
                    // 等待一小段时间后重试
                    std::this_thread::sleep_for(std::chrono::milliseconds(500 * retry_count));
                    retry_update();
                    return;
                }

                // 不可重试的错误，标记完成
                operation_completed = true;
                return;
            }

            // 更新成功
            update_success = true;
            log_message(LOG_INFO, "已更新设备信息状态: chip_id=" + chip_id +
                        ", 状态: " + std::to_string(status));
            operation_completed = true;
        });
    };
    // 开始第一次尝试
    retry_update();

    // 等待操作完成，但最多等待2秒
    for(int i = 0; i < 20; i++)
    {
        if(operation_completed)
        {
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // 如果操作完成并且更新成功，返回true
    if(operation_completed && update_success)
    {
        return true;
    }

    // 如果操作尚未完成，记录警告但仍返回true
    // 因为操作是异步的，实际结果会在回调中处理
    if(!operation_completed)
    {
        log_message(LOG_WARN, "更新设备信息状态操作尚未完成，但程序将继续执行");
        return true;
    }

    return false;
}

// 检查任务是否已在MySQL中完成
bool is_task_completed_in_mysql(const std::string &chip_id, const std::string &datetime)
{
    if(!app_config.use_mysql)
    {
        return false; // MySQL功能未启用，认为任务未完成
    }

    // 检查连接池是否初始化
    auto &pool = MySQLConnectionPool::getInstance();

    if(!pool.isInitialized())
    {
        log_message(LOG_DEBUG, "MySQL连接池未初始化，无法检查任务状态");
        return false;
    }

    try
    {
        // 使用同步查询检查任务是否已完成
        std::string query = "SELECT process_status FROM device_info WHERE chip_id = '" +
                            chip_id + "' AND datetime = '" + datetime + "' AND process_status = 1 LIMIT 1";
        std::string error_msg;
        MYSQL_RES *result = pool.querySync(query, error_msg, 3); // 3秒超时

        if(!result)
        {
            if(!error_msg.empty())
            {
                log_message(LOG_DEBUG, "查询任务状态失败: " + error_msg);
            }

            return false; // 查询失败，认为任务未完成
        }

        // 检查是否有结果行
        MYSQL_ROW row = mysql_fetch_row(result);
        bool task_completed = (row != nullptr);
        // 释放结果集
        mysql_free_result(result);

        if(task_completed)
        {
            log_message(LOG_INFO, "任务已在MySQL中完成，跳过处理: " + chip_id + "_" + datetime);
            return true;
        }
        else
        {
            log_message(LOG_DEBUG, "任务未完成，继续处理: " + chip_id + "_" + datetime);
            return false;
        }
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, "检查MySQL任务状态时发生异常: " + std::string(e.what()));
        return false; // 异常时认为任务未完成
    }
}

// 初始化MySQL连接
bool init_mysql_connection()
{
    if(!app_config.use_mysql)
    {
        return true; // MySQL功能未启用，直接返回成功
    }

    // 使用连接池
    auto &pool = MySQLConnectionPool::getInstance();

    // 如果连接池已初始化，先关闭
    if(pool.isInitialized())
    {
        pool.shutdown();
    }

    // 初始化连接池
    bool success = pool.initialize(
                                   app_config.mysql_host,
                                   app_config.mysql_port,
                                   app_config.mysql_user,
                                   app_config.mysql_pass,
                                   app_config.mysql_db,
                                   3  // 初始连接数
                   );

    if(!success)
    {
        log_message(LOG_ERROR, "初始化MySQL连接池失败");
        return false;
    }

    // 异步创建表
    pool.executeAsync(
                    "SHOW TABLES LIKE 'device_info'",
                    [](bool success, const std::string & error)
    {
        if(!success)
        {
            log_message(LOG_ERROR, "检查设备信息表失败: " + error);
            return;
        }

        // 创建表的SQL语句
        const char *create_table_sql =
                        "CREATE TABLE IF NOT EXISTS device_info ("
                        "  id INT AUTO_INCREMENT PRIMARY KEY,"
                        "  receive_time DATETIME NOT NULL,"
                        "  chip_id VARCHAR(32) NOT NULL,"
                        "  datetime VARCHAR(20) NOT NULL,"
                        "  log_file_name VARCHAR(255) NOT NULL,"
                        "  log_file_size BIGINT UNSIGNED NOT NULL,"
                        "  total_memory BIGINT UNSIGNED NOT NULL,"
                        "  free_memory BIGINT UNSIGNED NOT NULL,"
                        "  temperature FLOAT NOT NULL,"
                        "  hardware_version VARCHAR(50) NOT NULL,"
                        "  system_version VARCHAR(50) NOT NULL,"
                        "  software_version VARCHAR(50) NOT NULL,"
                        "  machine_model VARCHAR(50) NOT NULL,"
                        "  file_checksum VARCHAR(64) NOT NULL,"
                        "  process_status TINYINT DEFAULT 0 NOT NULL,"
                        "  processed_time DATETIME NULL,"
                        "  processed_size BIGINT UNSIGNED DEFAULT 0,"
                        "  actual_checksum VARCHAR(64) DEFAULT '',"
                        "  error_message VARCHAR(255) DEFAULT '',"
                        "  INDEX (chip_id, datetime)"
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        // 异步创建表
        MySQLConnectionPool::getInstance().executeAsync(
                        create_table_sql,
                        [](bool success, const std::string & error)
        {
            if(!success)
            {
                log_message(LOG_ERROR, "创建设备信息表失败: " + error);
            }
            else
            {
                log_message(LOG_INFO, "设备信息表结构已是最新");
            }
        }
        );
    }
    );
    log_message(LOG_INFO, "已成功连接到MySQL数据库: " + app_config.mysql_db);
    return true;
}

// 关闭MySQL连接
void close_mysql_connection()
{
    // 关闭连接池
    auto &pool = MySQLConnectionPool::getInstance();

    if(pool.isInitialized())
    {
        pool.shutdown();
        log_message(LOG_INFO, "MySQL连接池已关闭");
    }
}

// 创建设备信息表 - 现在由连接池异步处理
bool create_device_info_table()
{
    // 此函数现在只是一个存根，实际创建表的逻辑已移至init_mysql_connection中
    // 保留此函数是为了兼容性
    return app_config.use_mysql && MySQLConnectionPool::getInstance().isInitialized();
}

// 主程序入口
int main(int argc, char **argv)
{
    // 记录启动时间
    program_start_time = time(nullptr);
    // 解析命令行参数
    app_config = parse_command_line(argc, argv);

    // 初始化应用
    if(!initialize())
    {
        std::cerr << "初始化失败" << std::endl;
        return 1;
    }

    // 如果启用守护进程模式
    if(app_config.run_as_daemon)
    {
        if(!daemonize())
        {
            log_message(LOG_ERROR, "无法启动守护进程");
            return 1;
        }
    }

    log_message(LOG_INFO, "=== Kafka数据导出服务启动 ===");
    log_message(LOG_INFO, "配置信息:");
    log_message(LOG_INFO, "- Kafka地址: " + app_config.kafka_brokers);
    log_message(LOG_INFO, "- 输出目录: " + app_config.output_dir);
    log_message(LOG_INFO, "- 启用排序: " + std::string(app_config.sort_messages ? "是" : "否"));
    log_message(LOG_INFO, "- 自动填充: " + std::string(app_config.auto_fill ? "是" : "否"));
    log_message(LOG_INFO, "- 状态文件: " + app_config.state_file);
    log_message(LOG_INFO, "- 设备信息表格: " + device_info_csv_path);

    if(app_config.use_mysql)
    {
        log_message(LOG_INFO, "- MySQL数据库: " + app_config.mysql_db);
        log_message(LOG_INFO, "  - 主机: " + app_config.mysql_host + ":" + std::to_string(app_config.mysql_port));
        log_message(LOG_INFO, "  - 用户: " + app_config.mysql_user);
    }
    else
    {
        log_message(LOG_INFO, "- MySQL: 未启用");
    }

    log_message(LOG_INFO, "- 消费模式: " + app_config.consume_mode);
    log_message(LOG_INFO, "- 固定线程池: 32主题 × 200分区 = 6400线程");
    // 创建固定线程池
    thread_pool = new FixedThreadPool(app_config);

    if(!thread_pool->initialize())
    {
        log_message(LOG_ERROR, "固定线程池初始化失败");
        cleanup();
        return 1;
    }

    log_message(LOG_INFO, "固定线程池初始化成功，所有工作线程已就绪");
    // 创建设备信息消费者
    RdKafka::KafkaConsumer *info_consumer = create_device_info_consumer();

    if(!info_consumer)
    {
        log_message(LOG_ERROR, "无法创建设备信息消费者");
        cleanup();
        return 1;
    }

    // 主循环 - 获取设备信息
    int message_count = 0;
    time_t last_state_save_time = time(nullptr);
    time_t last_consumer_check_time = time(nullptr);
    time_t last_message_time = time(nullptr);
    int consecutive_errors = 0;
    const int MAX_CONSECUTIVE_ERRORS = 10;
    const int CONSUMER_CHECK_INTERVAL = 300; // 5分钟检查一次消费者状态
    log_message(LOG_INFO, "开始监听设备信息");

    while(running)
    {
        time_t now = time(nullptr);

        // 检查是否需要强制重置消费者
        if(force_consumer_reset.load())
        {
            log_message(LOG_INFO, "收到强制重置消费者信号，正在重新创建消费者...");

            if(info_consumer)
            {
                info_consumer->close();
                delete info_consumer;
            }

            info_consumer = create_device_info_consumer();

            if(!info_consumer)
            {
                log_message(LOG_ERROR, "强制重置消费者失败");
            }
            else
            {
                log_message(LOG_INFO, "消费者已强制重置");
                last_message_time = now; // 重置消息时间
                last_consumer_check_time = now;
            }

            force_consumer_reset.store(false);
        }

        // 定期检查消费者状态
        if(now - last_consumer_check_time >= CONSUMER_CHECK_INTERVAL)
        {
            log_message(LOG_INFO, "检查消费者状态... (距离上次消息: " +
                        std::to_string(now - last_message_time) + "秒)");

            // 检查消费者是否还存在
            if(!info_consumer)
            {
                log_message(LOG_ERROR, "消费者对象为空，尝试重新创建");
                info_consumer = create_device_info_consumer();

                if(!info_consumer)
                {
                    log_message(LOG_ERROR, "重新创建消费者失败");
                    std::this_thread::sleep_for(std::chrono::seconds(30));
                    continue;
                }

                log_message(LOG_INFO, "消费者已重新创建");
                last_consumer_check_time = now;
                continue;
            }

            // 获取分配的分区信息
            std::vector<RdKafka::TopicPartition *> partitions;
            RdKafka::ErrorCode err = info_consumer->assignment(partitions);

            if(err == RdKafka::ERR_NO_ERROR)
            {
                if(partitions.empty())
                {
                    log_message(LOG_WARN, "消费者没有分配到任何分区，尝试重新连接");
                    // 强制重新创建消费者
                    info_consumer->close();
                    delete info_consumer;
                    std::this_thread::sleep_for(std::chrono::seconds(5));
                    info_consumer = create_device_info_consumer();

                    if(!info_consumer)
                    {
                        log_message(LOG_ERROR, "重新创建消费者失败，等待重试");
                        std::this_thread::sleep_for(std::chrono::seconds(30));
                        continue; // 继续尝试而不是退出
                    }

                    log_message(LOG_INFO, "消费者已重新创建");
                    last_consumer_check_time = now;
                    continue;
                }
                else
                {
                    log_message(LOG_INFO, "消费者已分配 " + std::to_string(partitions.size()) + " 个分区");
                    // 显示详细的分区和offset信息（只显示前5个分区，避免日志过多）
                    int displayed_partitions = 0;

                    for(auto *tp : partitions)
                    {
                        if(displayed_partitions >= 5)
                        {
                            break;    // 只显示前5个分区
                        }

                        // 获取当前offset位置
                        std::vector<RdKafka::TopicPartition *> query_partitions;
                        query_partitions.push_back(RdKafka::TopicPartition::create(tp->topic(), tp->partition()));
                        RdKafka::ErrorCode pos_err = info_consumer->position(query_partitions);

                        if(pos_err == RdKafka::ERR_NO_ERROR && !query_partitions.empty())
                        {
                            log_message(LOG_DEBUG, "分区 " + tp->topic() + "[" + std::to_string(tp->partition()) +
                                        "] 当前offset: " + std::to_string(query_partitions[0]->offset()));
                        }
                        else
                        {
                            log_message(LOG_DEBUG, "无法获取分区 " + tp->topic() + "[" + std::to_string(tp->partition()) +
                                        "] 的offset信息: " + RdKafka::err2str(pos_err));
                        }

                        // 清理查询分区对象
                        for(auto *qtp : query_partitions)
                        {
                            delete qtp;
                        }

                        displayed_partitions++;
                    }

                    if(partitions.size() > 5)
                    {
                        log_message(LOG_DEBUG, "... 还有 " + std::to_string(partitions.size() - 5) + " 个分区未显示");
                    }
                }

                // 清理分区对象
                for(auto *tp : partitions)
                {
                    delete tp;
                }
            }
            else
            {
                log_message(LOG_WARN, "无法获取消费者分区分配信息: " + RdKafka::err2str(err));
            }

            // 检查消费者组状态
            RdKafka::Metadata *metadata;
            RdKafka::ErrorCode meta_err = info_consumer->metadata(false, nullptr, &metadata, 5000);

            if(meta_err == RdKafka::ERR_NO_ERROR)
            {
                log_message(LOG_INFO, "Kafka集群连接正常: " + std::string(metadata->orig_broker_name()));
                // 检查fd_info主题是否存在
                bool topic_found = false;

                for(auto topic_meta : *metadata->topics())
                {
                    if(topic_meta->topic() == "fd_info")
                    {
                        topic_found = true;
                        log_message(LOG_INFO, "fd_info主题存在，分区数: " + std::to_string(topic_meta->partitions()->size()));
                        // 显示每个分区的详细信息（只显示前3个）
                        int partition_count = 0;

                        for(auto partition_meta : *topic_meta->partitions())
                        {
                            if(partition_count >= 3)
                            {
                                break;
                            }

                            log_message(LOG_DEBUG, "fd_info分区[" + std::to_string(partition_meta->id()) +
                                        "] 领导者: " + std::to_string(partition_meta->leader()) +
                                        ", 错误: " + RdKafka::err2str(partition_meta->err()));
                            partition_count++;
                        }

                        break;
                    }
                }

                if(!topic_found)
                {
                    log_message(LOG_ERROR, "fd_info主题不存在！");
                    // 列出所有可用主题（只显示前10个）
                    log_message(LOG_INFO, "可用主题列表:");
                    int topic_count = 0;

                    for(auto topic_meta : *metadata->topics())
                    {
                        if(topic_count >= 10)
                        {
                            break;
                        }

                        log_message(LOG_INFO, "  - " + topic_meta->topic());
                        topic_count++;
                    }
                }

                delete metadata;
            }
            else
            {
                log_message(LOG_ERROR, "无法获取Kafka元数据: " + RdKafka::err2str(meta_err));
            }

            last_consumer_check_time = now;
        }

        RdKafka::Message *msg = info_consumer->consume(POLL_TIMEOUT_MS);
        // 每10秒打印一次消费状态
        static time_t last_consume_log = 0;

        if(now - last_consume_log >= 10)
        {
            log_message(LOG_DEBUG, "fd_info消费者轮询状态: " + std::string(msg ? "有消息" : "无消息"));
            last_consume_log = now;
        }

        if(msg)
        {
            switch(msg->err())
            {
                case RdKafka::ERR_NO_ERROR:
                    {
                        log_message(LOG_DEBUG, "fd_info消息处理开始，分区: " + std::to_string(msg->partition()) +
                                    ", offset: " + std::to_string(msg->offset()) +
                                    ", 消息大小: " + std::to_string(msg->len()) + " 字节");
                        // 重置错误计数器和更新最后消息时间
                        consecutive_errors = 0;
                        last_message_time = now;
                        // 解析设备信息
                        binary_stream::DeviceInfo device_info;
                        log_message(LOG_DEBUG, "尝试解析设备信息，消息大小: " + std::to_string(msg->len()) + " 字节");

                        if(device_info.ParseFromArray(msg->payload(), static_cast<int>(msg->len())))
                        {
                            log_message(LOG_DEBUG, "fd_info消息解析成功，芯片ID: " + device_info.chip_id() +
                                        ", 文件: " + device_info.log_file_name());

                            // 检查任务是否已在MySQL中完成
                            if(app_config.use_mysql && is_task_completed_in_mysql(device_info.chip_id(), device_info.datetime()))
                            {
                                log_message(LOG_INFO, "任务已完成，跳过处理: " + device_info.chip_id() + "_" + device_info.datetime());
                                delete msg;
                                continue;
                            }

                            // 导出设备信息到CSV表格
                            export_device_info_to_csv(device_info);

                            // 如果启用了MySQL，保存到数据库
                            if(app_config.use_mysql)
                            {
                                save_device_info_to_mysql(device_info);
                            }

                            // 创建任务信息
                            TaskInfo task(device_info);

                            // 检查对应线程是否空闲，如果忙则等待
                            if(!thread_pool->isThreadIdle(device_info.chip_id()))
                            {
                                log_message(LOG_DEBUG, "线程忙碌，等待空闲: " + device_info.chip_id());

                                // 等待线程空闲，最多等待30秒
                                if(!thread_pool->waitForThreadIdle(device_info.chip_id(), 30))
                                {
                                    log_message(LOG_WARN, "等待线程空闲超时，跳过任务: " + device_info.chip_id());
                                    delete msg;
                                    continue;
                                }
                            }

                            // 提交任务到固定线程池
                            if(thread_pool->submitTask(task))
                            {
                                message_count++;
                                int active_task_count = thread_pool->getActiveTaskCount();
                                log_message(LOG_INFO, "任务已提交: " + device_info.chip_id() +
                                            ", 文件: " + device_info.log_file_name() +
                                            ", 大小: " + std::to_string(device_info.log_file_size()) + " 字节" +
                                            ", 线程索引: " + std::to_string(task.partition) +
                                            ", 活动任务数: " + std::to_string(active_task_count));
                                // 任务添加成功后，手动提交offset确保数据完整性
                                RdKafka::ErrorCode commit_err = info_consumer->commitSync(msg);

                                if(commit_err != RdKafka::ERR_NO_ERROR)
                                {
                                    log_message(LOG_WARN, "提交offset失败: " + RdKafka::err2str(commit_err));
                                }
                                else
                                {
                                    log_message(LOG_DEBUG, "Offset已提交: " + std::to_string(msg->offset()));
                                }
                            }
                            else
                            {
                                log_message(LOG_WARN, "任务提交失败: " + device_info.chip_id() +
                                            ", 文件: " + device_info.log_file_name() +
                                            ", 不提交offset，等待重试");
                            }
                        }
                        else
                        {
                            log_message(LOG_ERROR, "设备信息解析错误，消息大小: " + std::to_string(msg->len()) + " 字节");

                            // 打印消息的前几个字节用于调试
                            if(msg->len() > 0)
                            {
                                std::string hex_dump;
                                const unsigned char *data = static_cast<const unsigned char *>(msg->payload());
                                int dump_len = std::min(static_cast<size_t>(32), msg->len());

                                for(int i = 0; i < dump_len; i++)
                                {
                                    char hex_byte[4];
                                    snprintf(hex_byte, sizeof(hex_byte), "%02x ", data[i]);
                                    hex_dump += hex_byte;
                                }

                                log_message(LOG_ERROR, "消息前" + std::to_string(dump_len) + "字节(hex): " + hex_dump);
                            }

                            consecutive_errors++;
                        }

                        break;
                    }

                case RdKafka::ERR__PARTITION_EOF:
                    // 已到达分区末尾，正常情况
                    break;

                case RdKafka::ERR__TIMED_OUT:
                    // 超时，正常情况
                    break;

                default:
                    log_message(LOG_ERROR, "设备信息消费错误: " + msg->errstr());
                    consecutive_errors++;

                    // 如果是严重错误，考虑重连
                    if(msg->err() == RdKafka::ERR__TRANSPORT ||
                            msg->err() == RdKafka::ERR__ALL_BROKERS_DOWN ||
                            msg->err() == RdKafka::ERR__RESOLVE)
                    {
                        log_message(LOG_WARN, "检测到严重的Kafka连接错误，可能需要重连");
                    }

                    break;
            }

            delete msg;
        }

        // 检查是否需要重连消费者
        if(consecutive_errors >= MAX_CONSECUTIVE_ERRORS)
        {
            log_message(LOG_ERROR, "连续错误次数过多(" + std::to_string(consecutive_errors) +
                        ")，尝试重新创建消费者");
            // 关闭当前消费者
            info_consumer->close();
            delete info_consumer;
            // 等待一段时间后重新创建
            std::this_thread::sleep_for(std::chrono::seconds(10));
            // 重新创建消费者
            info_consumer = create_device_info_consumer();

            if(!info_consumer)
            {
                log_message(LOG_ERROR, "重新创建消费者失败，等待重试");
                std::this_thread::sleep_for(std::chrono::seconds(30));
                continue; // 继续尝试而不是退出
            }

            log_message(LOG_INFO, "消费者已重新创建");
            consecutive_errors = 0;
            last_consumer_check_time = now;
        }

        // 检查长时间没有消息的情况
        if(now - last_message_time > 1800)   // 30分钟没有消息
        {
            log_message(LOG_WARN, "长时间没有接收到消息(" +
                        std::to_string(now - last_message_time) + "秒)，检查Kafka连接状态");
            // 获取消费者元数据来检查连接状态
            RdKafka::Metadata *metadata;
            RdKafka::ErrorCode err = info_consumer->metadata(true, nullptr, &metadata, 5000);

            if(err == RdKafka::ERR_NO_ERROR)
            {
                log_message(LOG_INFO, "Kafka连接正常，集群: " + std::string(metadata->orig_broker_name()));
                delete metadata;
                // 即使连接正常，但长时间没有消息，也尝试重新创建消费者
                log_message(LOG_WARN, "虽然Kafka连接正常，但长时间没有消息，尝试重新创建消费者");
                info_consumer->close();
                delete info_consumer;
                std::this_thread::sleep_for(std::chrono::seconds(5));
                info_consumer = create_device_info_consumer();

                if(!info_consumer)
                {
                    log_message(LOG_ERROR, "重新创建消费者失败，等待重试");
                    last_message_time = now; // 重置时间，避免频繁检查
                    continue; // 继续尝试而不是退出
                }

                log_message(LOG_INFO, "消费者已重新创建");
            }
            else
            {
                log_message(LOG_WARN, "Kafka连接检查失败: " + RdKafka::err2str(err) + "，尝试重新创建消费者");
                info_consumer->close();
                delete info_consumer;
                std::this_thread::sleep_for(std::chrono::seconds(5));
                info_consumer = create_device_info_consumer();

                if(!info_consumer)
                {
                    log_message(LOG_ERROR, "重新创建消费者失败，等待重试");
                    last_message_time = now; // 重置时间，避免频繁检查
                    continue; // 继续尝试而不是退出
                }

                log_message(LOG_INFO, "消费者已重新创建");
            }

            last_message_time = now; // 重置时间，避免频繁检查
        }

        // 定期健康检查
        if(now - last_state_save_time >= EXP_STATE_SAVE_INTERVAL)
        {
            if(thread_pool && thread_pool->isHealthy())
            {
                log_message(LOG_DEBUG, "系统健康检查正常");
            }
            else
            {
                log_message(LOG_WARN, "系统健康检查失败，但继续运行");
            }

            last_state_save_time = now;
        }

        // 打印统计信息 - 只在消息计数变化时打印
        static int last_printed_count = 0;

        if(app_config.verbose_level >= 1 && message_count > 0 &&
                (message_count % 10 == 0) && (message_count != last_printed_count))
        {
            int active_tasks = thread_pool ? thread_pool->getActiveTaskCount() : 0;
            log_message(LOG_INFO, "已处理: " + std::to_string(message_count) + " 条设备信息, 活动任务: " +
                        std::to_string(active_tasks) + "/6400");
            last_printed_count = message_count;
        }
    }

    // 清理资源
    log_message(LOG_INFO, "服务关闭中...");

    if(info_consumer)
    {
        info_consumer->close();
        delete info_consumer;
    }

    cleanup();
    log_message(LOG_INFO, "服务已关闭");
    const int MAX_SHUTDOWN_WAIT_TIME = 10; // 10秒超时
    time_t shutdown_start = time(nullptr);

    // 等待关闭完成
    while(time(nullptr) - shutdown_start < MAX_SHUTDOWN_WAIT_TIME)
    {
        // 检查是否有未完成的清理任务
        if(thread_pool == nullptr)
        {
            // 如果thread_pool已经被清理，则退出等待
            break;
        }

        // 短暂休眠以减少CPU占用
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    return 0;
}