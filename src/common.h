#ifndef COMMON_H
#define COMMON_H

#include <string>
#include <vector>
#include <cstdint>
#include "kafka_config.h"

/**
 * @brief 计算芯片ID对应的主题索引
 * @param chip_id 芯片ID
 * @return 主题索引（0-TOPIC_COUNT-1）
 */
int get_topic_index_for_chip_id(const std::string &chip_id);

/**
 * @brief 获取芯片ID对应的主题名称
 * @param chip_id 芯片ID
 * @return 主题完整名称（如 fd_log_01）
 */
std::string get_topic_for_chip_id(const std::string &chip_id);

/**
 * @brief 根据芯片ID计算对应的分区号
 * @param chip_id 芯片ID
 * @return 分区号（0-PARTITION_COUNT-1）
 */
int32_t get_partition_for_chip_id(const std::string &chip_id);

/**
 * @brief 生成所有可能的主题名称列表
 * @return 主题名称向量
 */
std::vector<std::string> generate_all_topics();

#endif // COMMON_H 