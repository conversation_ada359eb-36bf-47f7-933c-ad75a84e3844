#include "fixed_thread_pool.h"
#include "common.h"
#include "kafka_config.h"
#include <iostream>
#include <chrono>
#include <algorithm>
#include <fstream>

// 声明外部变量
extern AppConfig app_config;

// 计算总线程数：32个主题 × 200个分区 = 6400个线程
static const int TOTAL_THREADS = TOPIC_COUNT * PARTITION_COUNT;

FixedThreadPool::FixedThreadPool(const AppConfig &config)
    : config(config), running(false)
{
    // 初始化线程池数据结构
    worker_threads.reserve(TOTAL_THREADS);
    task_queues.resize(TOTAL_THREADS);
    queue_mutexes.reserve(TOTAL_THREADS);
    queue_cvs.reserve(TOTAL_THREADS);
    thread_stats.resize(TOTAL_THREADS);

    // 创建mutex和condition_variable对象
    for(int i = 0; i < TOTAL_THREADS; i++)
    {
        queue_mutexes.emplace_back(std::make_unique<std::mutex>());
        queue_cvs.emplace_back(std::make_unique<std::condition_variable>());
    }

    log_message(LOG_INFO, "固定线程池初始化，总线程数: " + std::to_string(TOTAL_THREADS));
}

FixedThreadPool::~FixedThreadPool()
{
    shutdown();
}

bool FixedThreadPool::initialize()
{
    if(running.load())
    {
        log_message(LOG_WARN, "线程池已经在运行");
        return true;
    }

    running.store(true);
    log_message(LOG_INFO, "启动固定线程池，创建 " + std::to_string(TOTAL_THREADS) + " 个工作线程...");

    // 创建所有工作线程
    for(int i = 0; i < TOTAL_THREADS; i++)
    {
        try
        {
            worker_threads.emplace_back(&FixedThreadPool::workerThreadFunc, this, i);
            // 初始化线程统计信息
            {
                std::lock_guard<std::mutex> lock(stats_mutex);
                thread_stats[i].thread_id = worker_threads[i].get_id();
                thread_stats[i].status = WorkerThreadStatus::IDLE;
            }

            // 每创建100个线程打印一次进度
            if((i + 1) % 100 == 0)
            {
                log_message(LOG_INFO, "已创建 " + std::to_string(i + 1) + "/" + std::to_string(TOTAL_THREADS) + " 个工作线程");
            }
        }
        catch(const std::exception &e)
        {
            log_message(LOG_ERROR, "创建工作线程 " + std::to_string(i) + " 失败: " + e.what());
            shutdown();
            return false;
        }
    }

    // 启动状态报告线程
    try
    {
        status_reporter_thread = std::thread(&FixedThreadPool::statusReporterFunc, this);
        log_message(LOG_INFO, "状态报告线程已启动");
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, std::string("启动状态报告线程失败: ") + e.what());
        shutdown();
        return false;
    }

    log_message(LOG_INFO, "固定线程池初始化完成，所有 " + std::to_string(TOTAL_THREADS) +
                " 个线程已就绪");
    return true;
}

void FixedThreadPool::shutdown()
{
    if(!running.load())
    {
        return;
    }

    log_message(LOG_INFO, "开始关闭固定线程池...");
    running.store(false);

    // 唤醒所有等待的线程
    for(auto &cv : queue_cvs)
    {
        cv->notify_all();
    }

    // 等待所有工作线程结束
    for(auto &thread : worker_threads)
    {
        if(thread.joinable())
        {
            thread.join();
        }
    }

    // 等待状态报告线程结束
    if(status_reporter_thread.joinable())
    {
        status_reporter_thread.join();
    }

    log_message(LOG_INFO, "固定线程池已关闭");
}

int FixedThreadPool::getThreadIndex(const std::string &chip_id) const
{
    // 根据芯片ID计算线程索引
    // 使用与原来相同的逻辑：主题索引 * PARTITION_COUNT + 分区索引
    int topic_index = get_topic_index_for_chip_id(chip_id);
    int partition_index = get_partition_for_chip_id(chip_id);
    return topic_index * PARTITION_COUNT + partition_index;
}

bool FixedThreadPool::submitTask(const TaskInfo &task)
{
    if(!running.load())
    {
        log_message(LOG_ERROR, "线程池未运行，无法提交任务");
        return false;
    }

    int thread_index = getThreadIndex(task.chip_id);

    if(thread_index < 0 || thread_index >= TOTAL_THREADS)
    {
        log_message(LOG_ERROR, "无效的线程索引: " + std::to_string(thread_index) + " (芯片ID: " + task.chip_id + ")");
        return false;
    }

    // 将任务添加到对应线程的队列
    {
        std::lock_guard<std::mutex> lock(*queue_mutexes[thread_index]);
        task_queues[thread_index].push(task);
    }
    // 唤醒对应的工作线程
    queue_cvs[thread_index]->notify_one();
    log_message(LOG_DEBUG, "任务已提交到线程 " + std::to_string(thread_index) +
                ": " + task.chip_id + "_" + task.datetime);
    return true;
}

bool FixedThreadPool::isThreadIdle(const std::string &chip_id) const
{
    int thread_index = getThreadIndex(chip_id);

    if(thread_index < 0 || thread_index >= TOTAL_THREADS)
    {
        return false;
    }

    std::lock_guard<std::mutex> lock(stats_mutex);
    return thread_stats[thread_index].status == WorkerThreadStatus::IDLE;
}

bool FixedThreadPool::waitForThreadIdle(const std::string &chip_id, int timeout_seconds)
{
    int thread_index = getThreadIndex(chip_id);

    if(thread_index < 0 || thread_index >= TOTAL_THREADS)
    {
        return false;
    }

    auto start_time = std::chrono::steady_clock::now();
    auto timeout_duration = std::chrono::seconds(timeout_seconds);

    while(std::chrono::steady_clock::now() - start_time < timeout_duration)
    {
        if(isThreadIdle(chip_id))
        {
            return true;
        }

        // 短暂休眠避免忙等待
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    log_message(LOG_WARN, "等待线程空闲超时: " + chip_id + " (线程索引: " + std::to_string(
                                thread_index) + ")");
    return false;
}

void FixedThreadPool::workerThreadFunc(int thread_index)
{
    log_message(LOG_DEBUG, "工作线程 " + std::to_string(thread_index) + " 已启动");
    updateThreadStats(thread_index, WorkerThreadStatus::IDLE);

    while(running.load())
    {
        TaskInfo task;
        bool has_task = false;
        // 等待任务
        {
            std::unique_lock<std::mutex> lock(queue_mutexes[thread_index]);
            queue_cvs[thread_index].wait(lock, [this, thread_index]
            {
                return !running.load() || !task_queues[thread_index].empty();
            });

            if(!running.load())
            {
                break;
            }

            if(!task_queues[thread_index].empty())
            {
                task = task_queues[thread_index].front();
                task_queues[thread_index].pop();
                has_task = true;
            }
        }

        if(has_task)
        {
            // 处理任务
            updateThreadStats(thread_index, WorkerThreadStatus::PROCESSING,
                              task.chip_id + "_" + task.datetime);
            bool success = processTask(thread_index, task);

            if(success)
            {
                updateThreadStats(thread_index, WorkerThreadStatus::IDLE, "",
                                  task.file_size, 1);
            }
            else
            {
                recordThreadError(thread_index, "任务处理失败: " + task.chip_id + "_" + task.datetime);
                updateThreadStats(thread_index, WorkerThreadStatus::IDLE);
            }
        }
    }

    updateThreadStats(thread_index, WorkerThreadStatus::STOPPED);
    log_message(LOG_DEBUG, "工作线程 " + std::to_string(thread_index) + " 已停止");
}

bool FixedThreadPool::processTask(int thread_index, const TaskInfo &task)
{
    log_message(LOG_INFO, "线程 " + std::to_string(thread_index) + " 开始处理任务: " +
                task.chip_id + "_" + task.datetime + " (文件: " + task.log_file_name +
                ", 大小: " + std::to_string(task.file_size) + " 字节)");

    try
    {
        // 创建Kafka消费者
        RdKafka::KafkaConsumer *consumer = createConsumer(task.topic, task.chip_id);

        if(!consumer)
        {
            log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 创建消费者失败");
            return false;
        }

        // 确保输出目录存在
        if(!ensureOutputDir(task.chip_id))
        {
            log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 创建输出目录失败");
            delete consumer;
            return false;
        }

        // 手动分配分区
        std::vector<RdKafka::TopicPartition *> partitions;
        partitions.push_back(RdKafka::TopicPartition::create(task.topic, task.partition));
        RdKafka::ErrorCode err = consumer->assign(partitions);

        if(err != RdKafka::ERR_NO_ERROR)
        {
            log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 分配分区失败: " + RdKafka::err2str(err));

            for(auto *tp : partitions)
            {
                delete tp;
            }

            delete consumer;
            return false;
        }

        log_message(LOG_INFO, "线程 " + std::to_string(thread_index) + " 已分配分区: " +
                    task.topic + "[" + std::to_string(task.partition) + "]");

        // 清理分区对象
        for(auto *tp : partitions)
        {
            delete tp;
        }

        // 开始消费和处理消息
        bool success = consumeAndProcessMessages(thread_index, consumer, task);
        // 清理资源
        consumer->close();
        delete consumer;

        if(success)
        {
            log_message(LOG_INFO, "线程 " + std::to_string(thread_index) + " 任务完成: " +
                        task.chip_id + "_" + task.datetime);
        }
        else
        {
            log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 任务失败: " +
                        task.chip_id + "_" + task.datetime);
        }

        return success;
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 处理任务异常: " + e.what());
        return false;
    }
}

void FixedThreadPool::updateThreadStats(int thread_index, WorkerThreadStatus status,
                                        const std::string &current_task,
                                        uint64_t bytes_processed,
                                        uint64_t messages_processed)
{
    std::lock_guard<std::mutex> lock(stats_mutex);

    if(thread_index >= 0 && thread_index < TOTAL_THREADS)
    {
        thread_stats[thread_index].status = status;
        thread_stats[thread_index].current_task = current_task;
        thread_stats[thread_index].last_activity_time = time(nullptr);

        if(bytes_processed > 0)
        {
            thread_stats[thread_index].total_bytes_processed += bytes_processed;
        }

        if(messages_processed > 0)
        {
            thread_stats[thread_index].total_messages_processed += messages_processed;
            thread_stats[thread_index].total_tasks_processed++;
        }
    }
}

void FixedThreadPool::recordThreadError(int thread_index, const std::string &error_msg)
{
    std::lock_guard<std::mutex> lock(stats_mutex);

    if(thread_index >= 0 && thread_index < TOTAL_THREADS)
    {
        thread_stats[thread_index].last_error = error_msg;
        thread_stats[thread_index].status = WorkerThreadStatus::ERROR;
    }

    log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 错误: " + error_msg);
}

int FixedThreadPool::getActiveTaskCount() const
{
    std::lock_guard<std::mutex> lock(stats_mutex);
    int active_count = 0;

    for(const auto &stats : thread_stats)
    {
        if(stats.status == WorkerThreadStatus::PROCESSING ||
                stats.status == WorkerThreadStatus::WAITING)
        {
            active_count++;
        }
    }

    return active_count;
}

std::vector<WorkerThreadStats> FixedThreadPool::getThreadStats() const
{
    std::lock_guard<std::mutex> lock(stats_mutex);
    return thread_stats;
}

void FixedThreadPool::statusReporterFunc()
{
    log_message(LOG_INFO, "状态报告线程已启动");

    while(running.load())
    {
        std::this_thread::sleep_for(std::chrono::seconds(30)); // 每30秒报告一次

        if(!running.load())
        {
            break;
        }

        printStatusReport();
    }

    log_message(LOG_INFO, "状态报告线程已停止");
}

void FixedThreadPool::printStatusReport() const
{
    std::lock_guard<std::mutex> lock(stats_mutex);
    int idle_count = 0;
    int processing_count = 0;
    int waiting_count = 0;
    int error_count = 0;
    int stopped_count = 0;
    uint64_t total_tasks = 0;
    uint64_t total_bytes = 0;
    uint64_t total_messages = 0;

    for(const auto &stats : thread_stats)
    {
        switch(stats.status)
        {
            case WorkerThreadStatus::IDLE:
                idle_count++;
                break;

            case WorkerThreadStatus::PROCESSING:
                processing_count++;
                break;

            case WorkerThreadStatus::WAITING:
                waiting_count++;
                break;

            case WorkerThreadStatus::ERROR:
                error_count++;
                break;

            case WorkerThreadStatus::STOPPED:
                stopped_count++;
                break;
        }

        total_tasks += stats.total_tasks_processed;
        total_bytes += stats.total_bytes_processed;
        total_messages += stats.total_messages_processed;
    }

    log_message(LOG_INFO, "=== 固定线程池状态报告 ===");
    log_message(LOG_INFO, "总线程数: " + std::to_string(TOTAL_THREADS));
    log_message(LOG_INFO, "空闲线程: " + std::to_string(idle_count));
    log_message(LOG_INFO, "处理中线程: " + std::to_string(processing_count));
    log_message(LOG_INFO, "等待中线程: " + std::to_string(waiting_count));
    log_message(LOG_INFO, "错误线程: " + std::to_string(error_count));
    log_message(LOG_INFO, "已停止线程: " + std::to_string(stopped_count));
    log_message(LOG_INFO, "总处理任务: " + std::to_string(total_tasks));
    log_message(LOG_INFO, "总处理字节: " + std::to_string(total_bytes));
    log_message(LOG_INFO, "总处理消息: " + std::to_string(total_messages));
    log_message(LOG_INFO, "========================");
}

bool FixedThreadPool::isHealthy() const
{
    std::lock_guard<std::mutex> lock(stats_mutex);
    int error_count = 0;
    int stopped_count = 0;

    for(const auto &stats : thread_stats)
    {
        if(stats.status == WorkerThreadStatus::ERROR)
        {
            error_count++;
        }
        else if(stats.status == WorkerThreadStatus::STOPPED)
        {
            stopped_count++;
        }
    }

    // 如果错误线程数超过10%或停止线程数超过5%，认为不健康
    return (error_count < TOTAL_THREADS * 0.1) && (stopped_count < TOTAL_THREADS * 0.05);
}

RdKafka::KafkaConsumer *FixedThreadPool::createConsumer(const std::string &topic, const std::string &chip_id)
{
    std::string errstr;
    RdKafka::Conf *conf = RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL);
    // 配置消费者
    std::map<std::string, std::string> kafka_config =
    {
        {"bootstrap.servers", config.kafka_brokers},
        {"enable.auto.commit", "false"},
        {"auto.offset.reset", config.consume_mode == "stored" ? "earliest" : config.consume_mode},
        {
            "group.id", config.consume_mode == "stored" ? "exporter_group_stable" :
            "exporter_group_" + config.consume_mode + "_" + std::to_string(time(nullptr))
        },
        {"session.timeout.ms", "30000"},
        {"heartbeat.interval.ms", "10000"},
        {"max.poll.interval.ms", "300000"},
        {"fetch.min.bytes", "1"},
        {"fetch.max.wait.ms", "500"}
    };

    for(const auto &pair : kafka_config)
    {
        if(conf->set(pair.first, pair.second, errstr) != RdKafka::Conf::CONF_OK)
        {
            log_message(LOG_ERROR, "设置Kafka配置失败: " + pair.first + " = " + pair.second + ", 错误: " + errstr);
            delete conf;
            return nullptr;
        }
    }

    // 创建消费者
    RdKafka::KafkaConsumer *consumer = RdKafka::KafkaConsumer::create(conf, errstr);
    delete conf;

    if(!consumer)
    {
        log_message(LOG_ERROR, "创建Kafka消费者失败: " + errstr);
        return nullptr;
    }

    return consumer;
}

bool FixedThreadPool::ensureOutputDir(const std::string &chip_id)
{
    std::string output_path = config.output_dir + "/" + chip_id;
    // 创建目录
    std::string cmd = "mkdir -p " + output_path;
    int result = system(cmd.c_str());

    if(result != 0)
    {
        log_message(LOG_ERROR, "创建输出目录失败: " + output_path);
        return false;
    }

    return true;
}

bool FixedThreadPool::consumeAndProcessMessages(int thread_index, RdKafka::KafkaConsumer *consumer,
        const TaskInfo &task)
{
    log_message(LOG_INFO, "线程 " + std::to_string(thread_index) + " 开始消费消息: " + task.topic + "[" +
                std::to_string(task.partition) + "]");
    updateThreadStats(thread_index, WorkerThreadStatus::WAITING, task.chip_id + "_" + task.datetime);
    std::string output_file = config.output_dir + "/" + task.chip_id + "/" + task.log_file_name;
    std::ofstream file(output_file, std::ios::binary);

    if(!file.is_open())
    {
        log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 无法创建输出文件: " + output_file);
        return false;
    }

    uint64_t total_bytes = 0;
    uint64_t message_count = 0;
    time_t start_time = time(nullptr);
    time_t last_message_time = start_time;

    // 消费消息直到文件完整
    while(running.load() && total_bytes < task.file_size)
    {
        RdKafka::Message *msg = consumer->consume(1000); // 1秒超时

        if(!msg)
        {
            continue;
        }

        switch(msg->err())
        {
            case RdKafka::ERR_NO_ERROR:
                {
                    // 检查消息是否属于当前任务
                    if(msg->len() >= task.chip_id.length() &&
                            memcmp(msg->payload(), task.chip_id.c_str(), task.chip_id.length()) == 0)
                    {
                        // 写入文件数据（跳过芯片ID部分）
                        const char *data = static_cast<const char *>(msg->payload()) + task.chip_id.length();
                        size_t data_len = msg->len() - task.chip_id.length();
                        file.write(data, data_len);
                        total_bytes += data_len;
                        message_count++;
                        last_message_time = time(nullptr);
                        // 提交offset
                        consumer->commitSync(msg);

                        if(message_count % 100 == 0)
                        {
                            log_message(LOG_DEBUG, "线程 " + std::to_string(thread_index) +
                                        " 已处理 " + std::to_string(message_count) + " 条消息, " +
                                        std::to_string(total_bytes) + "/" + std::to_string(task.file_size) + " 字节");
                        }
                    }
                }
                break;

            case RdKafka::ERR__PARTITION_EOF:
                log_message(LOG_DEBUG, "线程 " + std::to_string(thread_index) + " 到达分区末尾");
                break;

            case RdKafka::ERR__TIMED_OUT:

                // 检查超时
                if(time(nullptr) - last_message_time > 60)    // 60秒无消息则超时
                {
                    log_message(LOG_WARN, "线程 " + std::to_string(thread_index) + " 消息接收超时");
                    delete msg;
                    file.close();
                    return false;
                }

                break;

            default:
                log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 消费错误: " + msg->errstr());
                delete msg;
                file.close();
                return false;
        }

        delete msg;
    }

    file.close();
    bool success = (total_bytes == task.file_size);

    if(success)
    {
        log_message(LOG_INFO, "线程 " + std::to_string(thread_index) + " 文件接收完成: " +
                    output_file + " (" + std::to_string(total_bytes) + " 字节, " +
                    std::to_string(message_count) + " 条消息)");
    }
    else
    {
        log_message(LOG_ERROR, "线程 " + std::to_string(thread_index) + " 文件接收不完整: " +
                    std::to_string(total_bytes) + "/" + std::to_string(task.file_size) + " 字节");
    }

    return success;
}

// 全局函数实现
std::string get_topic_for_chip_id(const std::string &chip_id)
{
    int topic_index = get_topic_index_for_chip_id(chip_id);
    return get_topic_name_by_index(topic_index);
}

int32_t get_partition_for_chip_id(const std::string &chip_id)
{
    return get_partition_index_for_chip_id(chip_id);
}
