#ifndef FIXED_THREAD_POOL_H
#define FIXED_THREAD_POOL_H

#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <queue>
#include <vector>
#include <memory>
#include <map>
#include "binary_stream.pb.h"
#include "exporter.h"
#include "common.h"

// 固定线程池中的工作线程状态
enum class WorkerThreadStatus
{
    IDLE,           // 空闲
    PROCESSING,     // 处理中
    WAITING,        // 等待消息
    ERROR,          // 错误状态
    STOPPED         // 已停止
};

// 工作线程统计信息
struct WorkerThreadStats
{
    std::thread::id thread_id;
    WorkerThreadStatus status;
    std::string current_task;
    uint64_t total_tasks_processed;
    uint64_t total_bytes_processed;
    uint64_t total_messages_processed;
    time_t last_activity_time;
    time_t thread_start_time;
    std::string last_error;

    WorkerThreadStats() :
        status(WorkerThreadStatus::IDLE),
        total_tasks_processed(0),
        total_bytes_processed(0),
        total_messages_processed(0),
        last_activity_time(time(nullptr)),
        thread_start_time(time(nullptr)) {}
};

// 任务信息
struct TaskInfo
{
    std::string chip_id;
    std::string datetime;
    std::string log_file_name;
    uint64_t file_size;
    std::string topic;
    int32_t partition;

    TaskInfo() : file_size(0), partition(0) {}

    TaskInfo(const binary_stream::DeviceInfo &device_info) :
        chip_id(device_info.chip_id()),
        datetime(device_info.datetime()),
        log_file_name(device_info.log_file_name()),
        file_size(device_info.log_file_size())
    {
        // 计算主题和分区
        topic = get_topic_for_chip_id(chip_id);
        partition = get_partition_for_chip_id(chip_id);
    }
};

// 固定线程池类
class FixedThreadPool
{
  public:
    FixedThreadPool(const AppConfig &config);
    ~FixedThreadPool();

    // 初始化线程池
    bool initialize();

    // 停止线程池
    void shutdown();

    // 提交任务到指定线程的队列
    bool submitTask(const TaskInfo &task);

    // 检查指定线程是否空闲
    bool isThreadIdle(const std::string &chip_id) const;

    // 获取指定线程的队列大小
    size_t getThreadQueueSize(const std::string &chip_id) const;

    // 获取线程索引（公开接口）
    int getThreadIndex(const std::string &chip_id) const;

    // 获取线程统计信息
    std::vector<WorkerThreadStats> getThreadStats() const;

    // 获取活动任务数
    int getActiveTaskCount() const;

    // 打印状态报告
    void printStatusReport() const;

    // 健康检查
    bool isHealthy() const;

  private:
    const AppConfig &config;
    std::atomic<bool> running;

    // 线程池：每个主题分区对应一个固定线程
    std::vector<std::thread> worker_threads;
    std::vector<std::queue<TaskInfo>> task_queues;  // 每个线程一个任务队列
    std::vector<std::unique_ptr<std::mutex>> queue_mutexes;          // 每个队列一个互斥锁
    std::vector<std::unique_ptr<std::condition_variable>> queue_cvs; // 每个队列一个条件变量

    // 线程统计信息
    mutable std::mutex stats_mutex;
    std::vector<WorkerThreadStats> thread_stats;

    // 状态报告线程
    std::thread status_reporter_thread;



    // 工作线程函数
    void workerThreadFunc(int thread_index);

    // 处理单个任务
    bool processTask(int thread_index, const TaskInfo &task);

    // 状态报告线程函数
    void statusReporterFunc();

    // 更新线程统计信息
    void updateThreadStats(int thread_index, WorkerThreadStatus status,
                           const std::string &current_task = "",
                           uint64_t bytes_processed = 0,
                           uint64_t messages_processed = 0);

    // 记录线程错误
    void recordThreadError(int thread_index, const std::string &error_msg);

    // 创建Kafka消费者
    RdKafka::KafkaConsumer *createConsumer(const std::string &topic, const std::string &chip_id);

    // 确保输出目录存在
    bool ensureOutputDir(const std::string &chip_id);

    // 消费和处理消息
    bool consumeAndProcessMessages(int thread_index, RdKafka::KafkaConsumer *consumer, const TaskInfo &task);
};

// 注意：get_topic_for_chip_id 和 get_partition_for_chip_id 函数在 common.h 中声明

#endif // FIXED_THREAD_POOL_H
