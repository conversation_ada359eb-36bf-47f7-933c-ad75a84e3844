#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <cstdint>
#include <cstring>
#include <ctime>
#include <fstream>
#include <sys/stat.h>
#include <sys/time.h>
#include <librdkafka/rdkafkacpp.h>
#include "binary_stream.pb.h"
#include "kafka_config.h"
#include "common.h"  // 添加公共函数头文件
#include <functional> // 添加头文件，用于hash函数
#include <getopt.h> // 用于解析命令行参数
#include <random> // 添加随机数生成器头文件
#include <sstream>  // 添加这一行，用于istringstream
#include <unistd.h> // 添加这一行，用于gethostname和sysconf
#include <openssl/md5.h> // 添加OpenSSL MD5头文件
#include <iomanip> // 用于std::setw和std::setfill

#define MAX_BUFFER_SIZE 2048  // 最大数据包大小
#define MIN_BUFFER_SIZE 128   // 最小数据包大小
#define TOTAL_FRAMES 500     // 总共要发送的帧数
#define DEFAULT_CHIP_ID "b124ee90bffe9000"  // 默认芯片ID
#define POLL_INTERVAL 50      // 每50个消息poll一次
#define MAX_RETRY_COUNT 5     // 最大重试次数

// 全局状态
static int fake_data = 1;
static std::string program_name;
static std::string kafka_brokers = KAFKA_BROKERS;  // 默认使用配置文件中的地址
static MD5_CTX md5_context;      // MD5上下文，用于增量计算文件校验和
static std::string file_md5_sum; // 最终的MD5校验和

// 显示命令行帮助信息
static void show_usage(const char *program_name)
{
    std::cout << "使用方法: " << program_name << " [选项] [芯片ID]" << std::endl;
    std::cout << "  选项:" << std::endl;
    std::cout << "    -b, --brokers=LIST     指定Kafka服务器地址 (默认: " << KAFKA_BROKERS << ")" << std::endl;
    std::cout << "    -h, --help             显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "  芯片ID: 可选参数，16字节的芯片唯一标识符" << std::endl;
    std::cout << "          如果不指定，将使用默认值: " << DEFAULT_CHIP_ID << std::endl;
    std::cout << std::endl << "例子:" << std::endl;
    std::cout << "  " << program_name << std::endl;
    std::cout << "  " << program_name << " -b localhost:9092" << std::endl;
    std::cout << "  " << program_name << " a1b2c3d4e5f67890" << std::endl;
    std::cout << "  " << program_name << " -b localhost:9092 a1b2c3d4e5f67890" << std::endl;
}

// 解析命令行参数
static bool parse_command_line(int argc, char *argv[], std::string &chip_id)
{
    static struct option long_options[] =
    {
        {"brokers", required_argument, 0, 'b'},
        {"help",    no_argument,       0, 'h'},
        {0,         0,                 0,  0 }
    };
    int option_index = 0;
    int c;

    while((c = getopt_long(argc, argv, "b:h", long_options, &option_index)) != -1)
    {
        switch(c)
        {
            case 'b':
                kafka_brokers = optarg;
                std::cout << "使用自定义Kafka服务器地址: " << kafka_brokers << std::endl;
                break;

            case 'h':
                show_usage(argv[0]);
                return false;

            case '?':
                // getopt_long已经输出错误信息
                return false;

            default:
                std::cerr << "未知选项: " << (char)c << std::endl;
                return false;
        }
    }

    // 检查是否有剩余参数（芯片ID）
    if(optind < argc)
    {
        // 使用用户提供的芯片ID
        if(strlen(argv[optind]) > 16)
        {
            std::cerr << "错误: 芯片ID长度不能超过16字节" << std::endl;
            return false;
        }

        chip_id = argv[optind];
        std::cout << "使用用户指定的芯片ID: " << chip_id << std::endl;
    }
    else
    {
        // 使用默认芯片ID
        chip_id = DEFAULT_CHIP_ID;
        std::cout << "使用默认芯片ID: " << chip_id << std::endl;
    }

    return true;
}

// 确保输出目录存在
static bool ensure_output_dir()
{
    if(mkdir("../output", 0755) != 0 && errno != EEXIST)
    {
        std::cerr << "错误: 无法创建输出目录: " << strerror(errno) << std::endl;
        return false;
    }

    std::string dir = "../output/producer";

    if(mkdir(dir.c_str(), 0755) != 0 && errno != EEXIST)
    {
        std::cerr << "错误: 无法创建producer输出目录: " << strerror(errno) << std::endl;
        return false;
    }

    return true;
}

// 创建并打开合并文件
static std::ofstream create_merged_file(const std::string &chip_id, const std::string &datetime)
{
    // 确保输出目录存在
    if(!ensure_output_dir())
    {
        return std::ofstream();
    }

    // 确保芯片ID子目录存在
    std::string chip_dir = "../output/producer/" + chip_id;

    if(mkdir(chip_dir.c_str(), 0755) != 0 && errno != EEXIST)
    {
        std::cerr << "错误: 无法创建芯片ID目录: " << strerror(errno) << std::endl;
        return std::ofstream();
    }

    // 创建文件名
    std::string filename = "../output/producer/" + chip_id + "/" + chip_id + "_" + datetime + ".ulg";
    // 打开输出文件
    std::ofstream file(filename, std::ios::binary);

    if(!file)
    {
        std::cerr << "错误: 无法创建输出文件 " << filename << ": " << strerror(errno) << std::endl;
    }
    else
    {
        std::cout << "已创建原始数据文件: " << filename << std::endl;
    }

    return file;
}

// 计算简单的校验和
static uint32_t calculate_checksum(const uint8_t *data, size_t len)
{
    uint32_t checksum = 0;

    for(size_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }

    return checksum;
}

// 消息传递回调，处理消息发送成功或失败事件
class DeliveryReportCallback : public RdKafka::DeliveryReportCb
{
  public:
    DeliveryReportCallback() : success_count(0), error_count(0) {}

    void dr_cb(RdKafka::Message &message) override
    {
        if(message.err())
        {
            std::cerr << "错误: 消息发送失败: " << message.errstr() << std::endl;
            error_count++;
        }
        else
        {
            success_count++;
        }
    }

    uint64_t success_count;
    uint64_t error_count;
};

// 生成变长的模拟二进制数据
static size_t generate_binary_data(uint8_t *buffer, uint64_t seq_num)
{
    // 创建随机数生成器
    static std::random_device rd;  // 获取真随机种子
    static std::mt19937 gen(rd()); // 使用Mersenne Twister引擎
    // 创建均匀分布在[MIN_BUFFER_SIZE, MAX_BUFFER_SIZE]之间的随机数分布
    static std::uniform_int_distribution<size_t> dist(MIN_BUFFER_SIZE, MAX_BUFFER_SIZE);
    // 生成真随机的数据长度
    size_t data_length = dist(gen);
    // 使用明显的数据模式，填充序列号作为标记开始
    buffer[0] = 0xAA;  // 固定的起始标记
    buffer[1] = 0xBB;
    buffer[2] = 0xCC;
    buffer[3] = 0xDD;
    buffer[4] = (seq_num >> 24) & 0xFF;  // 高位字节
    buffer[5] = (seq_num >> 16) & 0xFF;
    buffer[6] = (seq_num >> 8) & 0xFF;
    buffer[7] = seq_num & 0xFF;          // 低位字节
    // 在标记后增加长度信息，便于验证
    buffer[8] = (data_length >> 24) & 0xFF;
    buffer[9] = (data_length >> 16) & 0xFF;
    buffer[10] = (data_length >> 8) & 0xFF;
    buffer[11] = data_length & 0xFF;

    // 剩余位置填充递增数据
    for(size_t i = 12; i < data_length; i++)
    {
        buffer[i] = fake_data;
    }

    fake_data++;
    return data_length;
}

// 包装函数用于设置Kafka配置并处理错误
void set_config(RdKafka::Conf *conf, const std::string &key, const std::string &value)
{
    std::string errstr;

    if(conf->set(key, value, errstr) != RdKafka::Conf::CONF_OK)
    {
        std::cerr << "错误: 无法设置配置: " << errstr << std::endl;
        exit(1);
    }
}

// 初始化MD5计算
void init_md5_context()
{
    MD5_Init(&md5_context);
    file_md5_sum.clear();
}

// 更新MD5计算
void update_md5_context(const uint8_t *data, size_t data_length)
{
    MD5_Update(&md5_context, data, data_length);
}

// 完成MD5计算并返回十六进制字符串
std::string finalize_md5_context()
{
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5_Final(digest, &md5_context);
    std::stringstream ss;

    for(int i = 0; i < MD5_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(digest[i]);
    }

    file_md5_sum = ss.str();
    return file_md5_sum;
}

int main(int argc, char **argv)
{
    std::string chip_id;
    std::string datetime;

    // 处理命令行参数
    if(!parse_command_line(argc, argv, chip_id))
    {
        return 1;
    }

    // 初始化MD5上下文
    init_md5_context();
    // 初始化随机数生成器
    srand(time(NULL));
    // 生成时间戳
    time_t now;
    struct tm *tm_info;
    time(&now);
    tm_info = localtime(&now);
    char datetime_buf[15];
    strftime(datetime_buf, sizeof(datetime_buf), "%Y%m%d%H%M%S", tm_info);
    datetime = datetime_buf;
    std::cout << "使用日期时间: " << datetime << std::endl;
    // 创建客户端配置
    std::string errstr;
    RdKafka::Conf *conf = RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL);
    // 设置Kafka配置
    conf->set("bootstrap.servers", kafka_brokers, errstr);
    // conf->set("bootstrap.servers", KAFKA_SASL_BROKERS, errstr);
    // conf->set("sasl.username",     KAFKA_SASL_KEY, errstr);
    // conf->set("sasl.password",     KAFKA_SASL_SECRET, errstr);
    // conf->set("security.protocol", "SASL_SSL", errstr);
    // conf->set("sasl.mechanisms",   "PLAIN", errstr);
    conf->set("acks", ACKS_CONFIG, errstr);
    conf->set("socket.timeout.ms", std::to_string(SOCKET_TIMEOUT_MS), errstr);
    conf->set("reconnect.backoff.max.ms", std::to_string(RECONNECT_BACKOFF_MAX_MS), errstr);
    conf->set("reconnect.backoff.ms", std::to_string(RECONNECT_BACKOFF_MS), errstr);
    // 创建传递报告回调
    DeliveryReportCallback dr_cb;
    conf->set("dr_cb", &dr_cb, errstr);
    // 设置消息队列大小限制，避免内存溢出
    conf->set("queue.buffering.max.messages", std::to_string(QUEUE_BUFFERING_MAX_MESSAGES), errstr);
    conf->set("queue.buffering.max.kbytes", std::to_string(QUEUE_BUFFERING_MAX_KBYTES), errstr);
    conf->set("linger.ms", std::to_string(LINGER_MS_CONFIG), errstr);
    conf->set("message.send.max.retries", std::to_string(MESSAGE_SEND_MAX_RETRIES), errstr);
    conf->set("retry.backoff.ms", std::to_string(RETRY_BACKOFF_MS), errstr);
    conf->set("request.timeout.ms", std::to_string(REQUEST_TIMEOUT_MS), errstr);
    // 创建Producer实例
    RdKafka::Producer *producer = RdKafka::Producer::create(conf, errstr);

    if(!producer)
    {
        std::cerr << "错误: 创建新的producer失败: " << errstr << std::endl;
        delete conf;
        return 1;
    }

    // 配置对象现在由producer实例拥有，无需手动删除
    // 主题名称，根据芯片ID计算
    std::string topic_name = get_topic_for_chip_id(chip_id);
    // 计算分区号
    int32_t partition = get_partition_for_chip_id(chip_id);
    std::cout << "使用主题: " << topic_name << ", 分区: " << partition << std::endl;
    // 创建合并文件
    std::ofstream merged_file = create_merged_file(chip_id, datetime);
    // 准备发送数据
    std::vector<uint8_t> buffer(MAX_BUFFER_SIZE);
    int sent_count = 0;
    size_t total_bytes_sent = 0;
    std::cout << "开始发送二进制流数据 (chip_id: " << chip_id << ", datetime: " << datetime << ")..." <<
              std::endl;

    for(uint64_t seq_num = 0; seq_num < TOTAL_FRAMES; seq_num++)
    {
        // 生成变长二进制数据
        size_t data_length = generate_binary_data(buffer.data(), seq_num);
        total_bytes_sent += data_length;
        // 更新MD5上下文
        update_md5_context(buffer.data(), data_length);

        // 保存到合并文件
        if(merged_file.is_open())
        {
            merged_file.write(reinterpret_cast<char *>(buffer.data()), data_length);

            if(!merged_file)
            {
                std::cerr << "错误: 写入原始数据到文件失败" << std::endl;
                merged_file.close();
            }
            else
            {
                merged_file.flush();
            }
        }

        // 创建并填充protobuf消息
        binary_stream::StreamData stream_data;
        stream_data.set_sequence_number(seq_num);
        stream_data.set_payload(buffer.data(), data_length);
        stream_data.set_chip_id(chip_id);
        stream_data.set_checksum(calculate_checksum(buffer.data(), data_length));
        stream_data.set_payload_length(data_length);
        struct timeval tv;
        gettimeofday(&tv, NULL);
        uint64_t timestamp = (uint64_t)tv.tv_sec * 1000 + (uint64_t)tv.tv_usec / 1000;
        stream_data.set_timestamp(timestamp);
        stream_data.set_is_last_frame(seq_num == TOTAL_FRAMES - 1);
        stream_data.set_datetime(datetime);
        // 序列化消息
        std::string serialized_data;

        if(!stream_data.SerializeToString(&serialized_data))
        {
            std::cerr << "错误: 无法序列化消息" << std::endl;
            break;
        }

        // 发送数据到Kafka，指定分区
        RdKafka::ErrorCode err = producer->produce(
                topic_name,
                partition, // 使用计算得到的固定分区
                RdKafka::Producer::RK_MSG_COPY, // 复制消息
                const_cast<char *>(serialized_data.c_str()), // 值
                serialized_data.size(), // 值大小
                chip_id.c_str(), // 键
                chip_id.size(), // 键大小
                0, // 时间戳（0表示当前）
                nullptr // 不使用消息不透明数据
                                 );

        if(err != RdKafka::ERR_NO_ERROR)
        {
            std::cerr << "错误: 发送数据到主题 " << topic_name << " 失败: " << RdKafka::err2str(err) << std::endl;

            // 如果是队列满了，等待并重试
            if(err == RdKafka::ERR__QUEUE_FULL)
            {
                std::cout << "消息队列已满，等待空间..." << std::endl;
                // 轮询事件，处理已完成的消息
                producer->poll(100);
                // 减小seq_num重试当前消息
                seq_num--;
                continue;
            }
            else
            {
                // 其他错误，停止发送
                if(merged_file.is_open())
                {
                    merged_file.close();
                }

                delete producer;
                return 1;
            }
        }
        else
        {
            sent_count++;

            // 每20个包显示一次进度
            if(seq_num > 0 && seq_num % 20 == 0)
            {
                std::cout << "已发送: " << seq_num << "/" << TOTAL_FRAMES << " 包, "
                          << "总数据量: " << total_bytes_sent << " 字节" << std::endl;
            }
        }

        // 定期轮询处理消息队列，避免积压太多
        if(seq_num % POLL_INTERVAL == 0)
        {
            producer->poll(0);
        }

        // 如果队列中消息超过一定数量，等待一些消息完成
        while(producer->outq_len() > QUEUE_BUFFERING_MAX_MESSAGES / 5)
        {
            std::cout << "队列中消息较多，等待一些消息完成..." << std::endl;
            producer->poll(100);
        }

        // 添加短暂延迟，模拟实际设备发送
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }

    std::cout << "消息生成完成，正在等待消息传递完成..." << std::endl;
    // 确保所有消息都被发送，使用多次flush和较长的超时
    int retry_count = 0;
    int remaining = 0;

    do
    {
        // 使用更长的超时时间确保消息发送
        std::cout << "正在等待消息队列清空，尝试 " << (retry_count + 1) << "/" << MAX_FLUSH_RETRY_COUNT
                  << " (队列中还有 " << producer->outq_len() << " 条消息)" << std::endl;
        producer->flush(FLUSH_TIMEOUT_MS);
        remaining = producer->outq_len();

        if(remaining > 0)
        {
            std::cout << "警告: 还有 " << remaining << " 条消息未能在超时时间内发送，将继续尝试..." <<
                      std::endl;
            // 处理一些事件，可能帮助解决一些问题
            producer->poll(1000);
            retry_count++;
        }
    } while(remaining > 0 && retry_count < MAX_FLUSH_RETRY_COUNT);

    if(producer->outq_len() > 0)
    {
        std::cerr << "警告: " << producer->outq_len() << " 条消息未能在多次尝试后发送" << std::endl;
    }

    // 关闭合并文件
    if(merged_file.is_open())
    {
        merged_file.close();
    }

    // 打印统计信息
    std::cout << "成功发送: " << sent_count << "/" << TOTAL_FRAMES << " 包" << std::endl;
    std::cout << "传递报告统计: 成功=" << dr_cb.success_count << ", 失败=" << dr_cb.error_count << std::endl;
    std::cout << "总数据量: " << total_bytes_sent << " 字节" << std::endl;
    // 完成MD5计算
    std::string file_checksum = finalize_md5_context();
    std::cout << "文件MD5校验和: " << file_checksum << std::endl;
    // 发送机器信息到fd_info主题
    std::cout << "发送设备信息到fd_info主题..." << std::endl;
    // 创建设备信息
    binary_stream::DeviceInfo device_info;
    device_info.set_chip_id(chip_id);
    device_info.set_machine_model("MODEL-A100");  // 示例，实际应从系统获取
    device_info.set_hardware_version("HW-2.0");   // 示例
    device_info.set_system_version("SYS-1.5");    // 示例
    device_info.set_software_version("SW-3.2.1"); // 示例
    // 尝试获取系统温度（示例方法，可能需要根据系统调整）
    float temp = 0.0f;
    std::ifstream temp_file("/sys/class/thermal/thermal_zone0/temp");

    if(temp_file)
    {
        int temp_milliC = 0;
        temp_file >> temp_milliC;
        temp = temp_milliC / 1000.0f;  // 转换为摄氏度
    }

    device_info.set_temperature(temp);
    // 获取内存信息
    uint64_t total_mem = 0, free_mem = 0;
    std::ifstream meminfo("/proc/meminfo");

    if(meminfo)
    {
        std::string line;

        while(std::getline(meminfo, line))
        {
            if(line.find("MemTotal:") == 0)
            {
                std::istringstream iss(line);
                std::string dummy;
                iss >> dummy >> total_mem;
            }
            else if(line.find("MemFree:") == 0)
            {
                std::istringstream iss(line);
                std::string dummy;
                iss >> dummy >> free_mem;
            }
        }
    }

    device_info.set_total_memory(total_mem);
    device_info.set_free_memory(free_mem);
    // 设置日志文件信息
    device_info.set_log_file_size(total_bytes_sent);
    device_info.set_log_file_name(chip_id + "_" + datetime + ".ulg");
    device_info.set_datetime(datetime);
    // 设置文件校验和
    device_info.set_file_checksum(file_checksum);
    // 添加一些扩展字段示例
    (*device_info.mutable_extended_fields())["cpu_cores"] = std::to_string(sysconf(_SC_NPROCESSORS_ONLN));
    (*device_info.mutable_extended_fields())["uptime"] = std::to_string(time(nullptr));
    (*device_info.mutable_extended_fields())["hostname"] = []
    {
        char hostname[256];
        gethostname(hostname, sizeof(hostname));
        return std::string(hostname);
    }();
    // 序列化消息
    std::string serialized_info;

    if(!device_info.SerializeToString(&serialized_info))
    {
        std::cerr << "错误: 无法序列化设备信息消息" << std::endl;
    }
    else
    {
        // 计算信息所属分区
        int32_t info_partition = get_partition_for_chip_id(chip_id);
        // 发送设备信息到fd_info主题
        RdKafka::ErrorCode err = producer->produce(
                "fd_info",              // 主题
                info_partition,         // 分区
                RdKafka::Producer::RK_MSG_COPY, // 复制消息
                const_cast<char *>(serialized_info.c_str()), // 值
                serialized_info.size(), // 值大小
                chip_id.c_str(),        // 键
                chip_id.size(),         // 键大小
                0,                      // 时间戳（0表示当前）
                nullptr                 // 不使用消息不透明数据
                                 );

        if(err != RdKafka::ERR_NO_ERROR)
        {
            std::cerr << "错误: 发送设备信息失败: " << RdKafka::err2str(err) << std::endl;
        }
        else
        {
            std::cout << "设备信息已发送到主题 fd_info, 分区 " << info_partition << std::endl;
            // 确保设备信息消息已发送
            producer->flush(5000);
        }
    }

    // 清理资源 - 移到最后
    delete producer;
    return 0;
}
