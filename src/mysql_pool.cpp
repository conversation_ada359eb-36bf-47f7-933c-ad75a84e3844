#include "mysql_pool.h"
#include "exporter.h"
#include <iostream>

// 构造函数
MySQLConnectionPool::MySQLConnectionPool()
    : running(false), active_connections(0), db_port(0),
      max_pool_size(0), initialized(false)
{
}

// 析构函数
MySQLConnectionPool::~MySQLConnectionPool()
{
    shutdown();
}

// 初始化连接池
bool MySQLConnectionPool::initialize(const std::string &host, int port,
                                     const std::string &user, const std::string &password,
                                     const std::string &database, int pool_size)
{
    // 如果已经初始化，先关闭
    if(initialized)
    {
        shutdown();
    }

    // 保存连接参数
    db_host = host;
    db_port = port;
    db_user = user;
    db_password = password;
    db_name = database;
    max_pool_size = pool_size;
    // 初始化MySQL库
    mysql_library_init(0, nullptr, nullptr);

    // 创建初始连接
    for(int i = 0; i < max_pool_size; i++)
    {
        MYSQL *conn = createConnection();

        if(conn)
        {
            connection_pool.push(conn);
        }
        else
        {
            log_message(LOG_WARN, "MySQL连接池初始化: 无法创建所有连接，只创建了 " +
                        std::to_string(i) + " 个连接");
        }
    }

    // 如果没有创建任何连接，初始化失败
    if(connection_pool.empty())
    {
        log_message(LOG_ERROR, "MySQL连接池初始化失败: 无法创建任何连接");
        return false;
    }

    // 启动工作线程
    running = true;

    for(int i = 0; i < max_pool_size; i++)
    {
        worker_threads.emplace_back(&MySQLConnectionPool::workerThread, this);
    }

    // 启动连接保活线程
    keepalive_thread = std::thread(&MySQLConnectionPool::keepaliveThread, this);
    initialized = true;
    log_message(LOG_INFO, "MySQL连接池初始化成功，创建了 " +
                std::to_string(connection_pool.size()) + " 个连接");
    return true;
}

// 关闭连接池
void MySQLConnectionPool::shutdown()
{
    if(!initialized)
    {
        return;
    }

    // 停止工作线程
    running = false;
    task_condition.notify_all();

    // 等待所有工作线程结束
    for(auto &thread : worker_threads)
    {
        if(thread.joinable())
        {
            thread.join();
        }
    }

    // 等待连接保活线程结束
    if(keepalive_thread.joinable())
    {
        keepalive_thread.join();
    }

    worker_threads.clear();
    // 关闭所有连接
    {
        std::lock_guard<std::mutex> lock(pool_mutex);

        while(!connection_pool.empty())
        {
            MYSQL *conn = connection_pool.front();
            connection_pool.pop();
            mysql_close(conn);
        }
    }
    // 清理MySQL库
    mysql_library_end();
    initialized = false;
    log_message(LOG_INFO, "MySQL连接池已关闭");
}

// 创建新连接
MYSQL *MySQLConnectionPool::createConnection()
{
    MYSQL *conn = mysql_init(nullptr);

    if(!conn)
    {
        log_message(LOG_ERROR, "无法初始化MySQL连接结构");
        return nullptr;
    }

    // 设置连接选项 - 使用更短的超时时间避免阻塞
    unsigned int connect_timeout = 5;   // 5秒连接超时
    unsigned int read_timeout = 10;     // 10秒读取超时
    unsigned int write_timeout = 10;    // 10秒写入超时
    mysql_options(conn, MYSQL_OPT_CONNECT_TIMEOUT, &connect_timeout);
    mysql_options(conn, MYSQL_OPT_READ_TIMEOUT, &read_timeout);
    mysql_options(conn, MYSQL_OPT_WRITE_TIMEOUT, &write_timeout);
    // 设置初始化命令 - 使用更长的超时时间
    const char *init_command = "SET SESSION wait_timeout=86400, interactive_timeout=86400";
    mysql_options(conn, MYSQL_INIT_COMMAND, init_command);
    // 设置自动重连
    bool reconnect = true;
    mysql_options(conn, MYSQL_OPT_RECONNECT, &reconnect);

    // 连接到MySQL服务器
    if(!mysql_real_connect(conn, db_host.c_str(), db_user.c_str(),
                           db_password.c_str(), db_name.c_str(),
                           db_port, nullptr, 0))
    {
        log_message(LOG_ERROR, "连接MySQL失败: " + std::string(mysql_error(conn)));
        mysql_close(conn);
        return nullptr;
    }

    // 设置字符集
    mysql_set_character_set(conn, "utf8mb4");
    return conn;
}

// 获取连接（有超时）
MYSQL *MySQLConnectionPool::getConnection(int timeout_seconds)
{
    std::unique_lock<std::mutex> lock(pool_mutex);
    // 等待可用连接，或者超时
    auto wait_result = pool_condition.wait_for(lock,
                       std::chrono::seconds(timeout_seconds),
                       [this] { return !connection_pool.empty(); });

    // 如果超时且没有可用连接
    if(!wait_result)
    {
        // 尝试创建一个新连接
        lock.unlock();
        MYSQL *new_conn = createConnection();

        if(new_conn)
        {
            active_connections++;
            return new_conn;
        }

        log_message(LOG_ERROR, "获取MySQL连接超时，且无法创建新连接");
        return nullptr;
    }

    // 获取一个连接
    MYSQL *conn = connection_pool.front();
    connection_pool.pop();
    active_connections++;
    return conn;
}

// 释放连接回池
void MySQLConnectionPool::releaseConnection(MYSQL *conn)
{
    if(!conn)
    {
        return;
    }

    // 检查连接是否仍然有效
    if(mysql_ping(conn) != 0)
    {
        // 连接已断开，关闭并创建新连接
        mysql_close(conn);
        conn = createConnection();

        if(!conn)
        {
            active_connections--;
            return;
        }
    }

    // 将连接放回池中
    {
        std::lock_guard<std::mutex> lock(pool_mutex);
        connection_pool.push(conn);
        active_connections--;
    }
    // 通知等待的线程
    pool_condition.notify_one();
}

// 异步执行SQL查询（不返回结果）
void MySQLConnectionPool::executeAsync(const std::string &sql,
                                       std::function<void(bool, const std::string &)> callback)
{
    if(!initialized)
    {
        if(callback)
        {
            callback(false, "MySQL连接池未初始化");
        }

        return;
    }

    // 创建任务并添加到队列
    auto task = std::make_shared<ExecuteTask>(sql, callback);
    {
        std::lock_guard<std::mutex> lock(task_mutex);
        task_queue.push(task);
    }
    // 通知工作线程
    task_condition.notify_one();
}

// 异步执行SQL查询（返回结果）
void MySQLConnectionPool::queryAsync(const std::string &sql,
                                     std::function<void(MYSQL_RES *, const std::string &)> callback)
{
    if(!initialized)
    {
        if(callback)
        {
            callback(nullptr, "MySQL连接池未初始化");
        }

        return;
    }

    // 创建任务并添加到队列
    auto task = std::make_shared<QueryTask>(sql, callback);
    {
        std::lock_guard<std::mutex> lock(task_mutex);
        task_queue.push(task);
    }
    // 通知工作线程
    task_condition.notify_one();
}

// 同步执行SQL查询（返回结果）
MYSQL_RES *MySQLConnectionPool::querySync(const std::string &sql, std::string &error_msg, int timeout_seconds)
{
    if(!initialized)
    {
        error_msg = "MySQL连接池未初始化";
        return nullptr;
    }

    // 获取连接
    MYSQL *conn = getConnection(timeout_seconds);

    if(!conn)
    {
        error_msg = "无法获取MySQL连接";
        return nullptr;
    }

    MYSQL_RES *res = nullptr;

    try
    {
        // 清理之前可能残留的结果
        while(mysql_next_result(conn) == 0)
        {
            MYSQL_RES *temp_res = mysql_store_result(conn);

            if(temp_res)
            {
                mysql_free_result(temp_res);
            }
        }

        // 执行SQL
        int result = mysql_query(conn, sql.c_str());

        if(result == 0)
        {
            res = mysql_store_result(conn);

            if(!res && mysql_field_count(conn) > 0)
            {
                error_msg = "无法获取查询结果: " + std::string(mysql_error(conn));
            }
        }
        else
        {
            error_msg = mysql_error(conn);
        }
    }
    catch(...)
    {
        error_msg = "查询执行时发生异常";

        if(res)
        {
            mysql_free_result(res);
            res = nullptr;
        }
    }

    // 释放连接
    releaseConnection(conn);
    return res;
}

// 工作线程函数
void MySQLConnectionPool::workerThread()
{
    while(running)
    {
        std::shared_ptr<Task> task;
        // 获取任务
        {
            std::unique_lock<std::mutex> lock(task_mutex);
            task_condition.wait_for(lock, std::chrono::seconds(1),
                                    [this] { return !task_queue.empty() || !running; });

            if(!running)
            {
                break;
            }

            if(task_queue.empty())
            {
                continue;
            }

            task = task_queue.front();
            task_queue.pop();
        }

        // 执行任务
        if(task)
        {
            task->execute();
        }
    }
}

// 执行查询任务（不返回结果）
void MySQLConnectionPool::ExecuteTask::execute()
{
    auto &pool = MySQLConnectionPool::getInstance();
    // 创建一个新的连接，而不是从连接池获取
    MYSQL *conn = pool.createConnection();

    if(!conn)
    {
        if(callback)
        {
            callback(false, "无法创建MySQL连接");
        }

        return;
    }

    // 执行SQL
    int result = mysql_query(conn, sql.c_str());
    std::string error_msg;

    if(result != 0)
    {
        error_msg = mysql_error(conn);
    }

    // 直接关闭连接，而不是释放回池
    mysql_close(conn);

    // 调用回调
    if(callback)
    {
        callback(result == 0, error_msg);
    }
}

// 连接保活线程函数
void MySQLConnectionPool::keepaliveThread()
{
    // 每60秒检查一次连接
    const int CHECK_INTERVAL_SECONDS = 60;

    while(running)
    {
        // 等待一段时间
        for(int i = 0; i < CHECK_INTERVAL_SECONDS && running; i++)
        {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }

        if(!running)
        {
            break;
        }

        // 刷新连接
        refreshConnections();
    }
}

// 检查并刷新连接
void MySQLConnectionPool::refreshConnections()
{
    if(!initialized)
    {
        return;
    }

    std::lock_guard<std::mutex> lock(pool_mutex);

    // 如果连接池为空，不需要刷新
    if(connection_pool.empty())
    {
        return;
    }

    // 记录连接池大小
    size_t pool_size = connection_pool.size();
    log_message(LOG_DEBUG, "刷新MySQL连接池中的 " + std::to_string(pool_size) + " 个连接");
    // 临时存储连接
    std::vector<MYSQL *> temp_connections;

    // 取出所有连接
    while(!connection_pool.empty())
    {
        MYSQL *conn = connection_pool.front();
        connection_pool.pop();

        // 检查连接是否有效
        if(mysql_ping(conn) != 0)
        {
            // 连接已断开，关闭并创建新连接
            log_message(LOG_WARN, "检测到断开的MySQL连接，正在重新创建");
            mysql_close(conn);
            conn = createConnection();

            if(!conn)
            {
                log_message(LOG_ERROR, "无法重新创建MySQL连接");
                continue;
            }
        }
        else
        {
            // 连接有效，执行一个简单查询以保持活跃
            if(mysql_query(conn, "SELECT 1") != 0)
            {
                log_message(LOG_WARN, "保活查询失败: " + std::string(mysql_error(conn)));
                mysql_close(conn);
                conn = createConnection();

                if(!conn)
                {
                    log_message(LOG_ERROR, "无法重新创建MySQL连接");
                    continue;
                }
            }
        }

        // 将有效连接添加到临时存储
        temp_connections.push_back(conn);
    }

    // 将所有有效连接放回连接池
    for(auto conn : temp_connections)
    {
        connection_pool.push(conn);
    }

    // 记录刷新结果
    if(temp_connections.size() < pool_size)
    {
        log_message(LOG_WARN, "MySQL连接池刷新: " + std::to_string(pool_size - temp_connections.size()) +
                    " 个连接已丢失，当前连接数: " + std::to_string(temp_connections.size()));
    }
    else
    {
        log_message(LOG_DEBUG, "MySQL连接池刷新完成，所有连接正常");
    }
}

// 执行查询任务（返回结果）
void MySQLConnectionPool::QueryTask::execute()
{
    auto &pool = MySQLConnectionPool::getInstance();
    // 创建一个新的连接，而不是从连接池获取
    MYSQL *conn = pool.createConnection();

    if(!conn)
    {
        if(callback)
        {
            callback(nullptr, "无法创建MySQL连接");
        }

        return;
    }

    // 执行SQL
    int result = mysql_query(conn, sql.c_str());
    MYSQL_RES *res = nullptr;
    std::string error_msg;

    if(result == 0)
    {
        res = mysql_store_result(conn);

        if(!res && mysql_field_count(conn) > 0)
        {
            error_msg = "无法获取查询结果: " + std::string(mysql_error(conn));
        }
    }
    else
    {
        error_msg = mysql_error(conn);
    }

    // 释放结果集前复制需要的数据
    MYSQL_RES *result_copy = nullptr;
    std::string error_copy = error_msg;

    if(res)
    {
        // 如果需要传递结果集给回调，创建一个副本
        // 这里我们简化处理，只传递错误信息，不传递实际结果集
        mysql_free_result(res);
    }

    // 直接关闭连接，而不是释放回池
    mysql_close(conn);

    // 调用回调（在释放所有MySQL资源后）
    if(callback)
    {
        callback(result_copy, error_copy);
    }
}
