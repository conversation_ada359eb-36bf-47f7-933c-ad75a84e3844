#include <iostream>
#include <string>
#include <cstdlib>
#include <librdkafka/rdkafkacpp.h>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <chrono>
#include <functional> // 用于std::hash
#include <cstdio>     // 用于snprintf
#include <openssl/sha.h>
#include <openssl/md5.h>
#include <unordered_map>
#include <mutex>
#include <fstream>
#include "common.h"

// 生成ISO 8601标准的时间戳字符串
std::string get_timestamp()
{
    auto now = std::chrono::system_clock::now();
    auto time_t_now = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                              now.time_since_epoch()) % 1000;
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t_now), "%Y-%m-%dT%H:%M:%S")
       << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

// 生成当前日期格式为YYYYMMDD的字符串
std::string get_date_string()
{
    auto now = std::chrono::system_clock::now();
    auto time_t_now = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t_now), "%Y%m%d");
    return ss.str();
}

static std::unordered_map<std::string, int> topic_cache;
static std::mutex cache_mutex;

/**
 * 计算芯片ID对应的主题索引
 * 确保producer和consumer使用相同的哈希方法
 */
int get_topic_index_for_chip_id(const std::string &chip_id)
{
    // 先检查缓存
    {
        std::lock_guard<std::mutex> lock(cache_mutex);
        auto it = topic_cache.find(chip_id);

        if(it != topic_cache.end())
        {
            return it->second;
        }
    }
    // 使用SHA-256哈希算法
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256(reinterpret_cast<const unsigned char *>(chip_id.data()),
           chip_id.size(), hash);
    // 将所有32字节的哈希值异或合并成一个整数
    uint64_t hash_value = 0;

    for(int i = 0; i < SHA256_DIGEST_LENGTH; i++)
    {
        hash_value ^= (static_cast<uint64_t>(hash[i]) << ((i % 8) * 8));
    }

    int topic_index = static_cast<int>(hash_value % TOPIC_COUNT);
    // 存入缓存
    {
        std::lock_guard<std::mutex> lock(cache_mutex);
        topic_cache[chip_id] = topic_index;
    }
    return topic_index;
}

/**
 * 获取芯片ID对应的主题名称
 * 结合前缀和索引，生成形如fd_log_XX的主题名
 */
std::string get_topic_for_chip_id(const std::string &chip_id)
{
    int topic_index = get_topic_index_for_chip_id(chip_id);
    char suffix[3];
    snprintf(suffix, sizeof(suffix), "%02x", topic_index);
    return TOPIC_PREFIX + std::string(suffix);
}

/**
 * 根据芯片ID计算分区
 * 使用二次哈希来确保分区均匀分布
 */
int32_t get_partition_for_chip_id(const std::string &chip_id)
{
    // 使用标准库的哈希函数对整个chip_id进行第二次哈希
    std::hash<std::string> hasher;
    size_t hash_value = hasher(chip_id);
    // 取模得到分区号，范围是0到PARTITION_COUNT-1
    return static_cast<int32_t>((hash_value / TOPIC_COUNT) % PARTITION_COUNT);
}

/**
 * 生成所有可能的主题列表
 * 用于全量消费场景
 */
std::vector<std::string> generate_all_topics()
{
    std::vector<std::string> topics;

    for(int i = 0; i < TOPIC_COUNT; i++)
    {
        char suffix[3];
        snprintf(suffix, sizeof(suffix), "%02x", i);
        topics.push_back(TOPIC_PREFIX + std::string(suffix));
    }

    return topics;
}

/**
 * 计算简单的校验和
 * 与producer中的算法保持一致
 */
uint32_t calculate_checksum(const void *data, size_t len)
{
    const uint8_t *bytes = static_cast<const uint8_t *>(data);
    uint32_t checksum = 0;

    for(size_t i = 0; i < len; i++)
    {
        checksum += bytes[i];
    }

    return checksum;
}

/**
 * 计算文件的MD5校验和
 */
std::string calculate_file_md5(const std::string &file_path)
{
    std::ifstream file(file_path, std::ios::binary);

    if(!file.is_open())
    {
        return "";
    }

    MD5_CTX md5_context;
    MD5_Init(&md5_context);
    char buffer[8192];

    while(file.read(buffer, sizeof(buffer)) || file.gcount() > 0)
    {
        MD5_Update(&md5_context, buffer, file.gcount());
    }

    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5_Final(digest, &md5_context);
    std::stringstream ss;

    for(int i = 0; i < MD5_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(digest[i]);
    }

    return ss.str();
}