syntax = "proto3";

package binary_stream;

message StreamData {
  // 帧序列号，用于保证数据顺序
  uint64 sequence_number = 1;
  
  // 二进制数据负载
  bytes payload = 2;
  
  // 芯片ID
  string chip_id = 3;
  
  // 校验和，用于确保数据完整性
  uint32 checksum = 4;
  
  // 时间戳
  uint64 timestamp = 5;
  
  // 是否是某个流的最后一个包
  bool is_last_frame = 6;
  
  // 数据长度信息
  uint32 payload_length = 7;
  
  // 日期时间，格式为年月日时分秒，共14位数字字符（如：20250415184350）
  string datetime = 8;
}

message DeviceInfo {
  string chip_id = 1;               // 芯片ID
  string machine_model = 2;         // 机器型号
  string hardware_version = 3;      // 硬件版本
  string system_version = 4;        // 系统版本
  string software_version = 5;      // 软件版本
  float temperature = 6;            // 温度
  uint64 total_memory = 7;          // 总内存(KB)
  uint64 free_memory = 8;           // 可用内存(KB)
  uint64 log_file_size = 9;         // 日志文件大小(bytes)
  string log_file_name = 10;        // 日志文件名称
  string datetime = 11;             // 时间戳
  
  // 扩展字段 - 使用map存储键值对
  map<string, string> extended_fields = 12;
  
  // 日志文件校验和 - 整个文件的MD5或SHA1校验和
  string file_checksum = 13;
} 