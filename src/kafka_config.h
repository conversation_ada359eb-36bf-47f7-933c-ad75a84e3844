#ifndef KAFKA_CONFIG_H
#define KAFKA_CONFIG_H

/**
 * @file kafka_config.h
 * @brief Kafka相关配置的统一管理文件
 * 此文件包含了所有与Kafka相关的配置参数，供producer和exporter_pro程序使用
 */

/* ================= 通用Kafka配置 ================= */

/**
 * @brief Kafka服务器地址
 * 指定Kafka集群的bootstrap服务器，格式为"host:port"或多个地址用逗号分隔
 */
#define KAFKA_BROKERS "**************:9092,**************:9093,**************:9094"
/**
 * @brief Socket保持连接
 * 是否启用TCP socket的keepalive功能，有助于识别断开的连接
 */
#define SOCKET_KEEPALIVE_ENABLE "true"

/**
 * @brief Kafka主题前缀
 * 所有相关主题的前缀，后面会追加两位十六进制数作为后缀
 */
#define TOPIC_PREFIX "fd_log_"

/**
 * @brief 主题数量
 * 系统中使用的主题总数，用于芯片ID哈希分配
 */
#define TOPIC_COUNT 32

/**
 * @brief 分区数量
 * 每个主题中的分区数量，用于数据分散存储
 */
#define PARTITION_COUNT 200

/* ================= 生产者(Producer)配置 ================= */

/**
 * @brief 确认模式
 * 指定生产者发送消息时的确认级别：
 * - "0": 不等待任何确认
 * - "1": 等待leader确认
 * - "all": 等待所有同步副本确认
 */
#define ACKS_CONFIG "all"

/**
 * @brief 延迟时间(ms)
 * 在批量发送之前等待的时间(毫秒)，以积累更多消息
 */
#define LINGER_MS_CONFIG 5

/**
 * @brief 批量大小(字节)
 * 一个批次中包含的最大字节数
 */
#define BATCH_SIZE_CONFIG 16384

/**
 * @brief Socket超时时间(ms)
 * 网络请求的socket连接超时时间
 */
#define SOCKET_TIMEOUT_MS 60000

/**
 * @brief 最大重连间隔(ms)
 * 连接失败后，尝试重新连接的最大等待时间
 */
#define RECONNECT_BACKOFF_MAX_MS 30000

/**
 * @brief 初始重连间隔(ms)
 * 连接失败后，首次尝试重新连接的等待时间
 */
#define RECONNECT_BACKOFF_MS 2000

/**
 * @brief 队列最大消息数
 * 生产者本地缓冲队列中允许的最大消息数
 */
#define QUEUE_BUFFERING_MAX_MESSAGES 500000

/**
 * @brief 队列最大字节数(KB)
 * 生产者本地缓冲队列中允许的最大字节数(KB)
 */
#define QUEUE_BUFFERING_MAX_KBYTES 512000

/**
 * @brief 消息发送最大重试次数
 * 在放弃前尝试重新发送消息的最大次数
 */
#define MESSAGE_SEND_MAX_RETRIES 10

/**
 * @brief 重试间隔(ms)
 * 重试之间的等待时间
 */
#define RETRY_BACKOFF_MS 100

/**
 * @brief 请求超时时间(ms)
 * 生产者请求的超时时间
 */
#define REQUEST_TIMEOUT_MS 30000

/**
 * @brief Flush超时时间(ms)
 * 强制刷新缓冲区的超时时间
 */
#define FLUSH_TIMEOUT_MS 120000

/**
 * @brief 轮询间隔消息数
 * 每发送多少条消息执行一次poll操作
 */
#define POLL_INTERVAL_MESSAGES 50

/**
 * @brief 最大flush重试次数
 * 程序退出前尝试flush的最大次数
 */
#define MAX_FLUSH_RETRY_COUNT 50

/* ================= 消费者(Consumer)配置 ================= */

/**
 * @brief 消费者组ID
 * 指定消费者所属的组，同组消费者共享消费进度
 */
#define CONSUMER_GROUP_ID "exporter_group"

/**
 * @brief 消费者请求超时(ms)
 * 消费者请求的超时时间，不同于生产者的request.timeout.ms
 */
#define CONSUMER_REQUEST_TIMEOUT_MS 60000

/**
 * @brief 消费者连接超时(ms)
 * 消费者建立连接的超时时间
 */
#define CONSUMER_CONNECTION_TIMEOUT_MS 30000

/**
 * @brief 组协调器连接重试次数
 * 连接到组协调器失败时的最大重试次数
 */
#define GROUP_COORDINATOR_CONNECT_RETRIES 10

/**
 * @brief 消费者轮询超时(ms)
 * 消费者轮询操作的超时时间
 */
#define POLL_TIMEOUT_MS 1000

/**
 * @brief 提交间隔(ms)
 * 自动提交偏移量的时间间隔
 */
#define COMMIT_INTERVAL_MS 5000

/**
 * @brief 会话超时(ms)
 * 消费者在被认为死亡前的非活动时间
 */
#define CONSUMER_SESSION_TIMEOUT_MS 6000000

/**
 * @brief Socket超时(ms)
 * 消费者Socket操作的超时时间
 */
#define CONSUMER_SOCKET_TIMEOUT_MS 1200000

/**
 * @brief 最大等待时间(ms)
 * 消费者在没有新消息时等待的最大时间
 */
#define FETCH_WAIT_MAX_MS 1000

/**
 * @brief 最大轮询间隔(ms)
 * 两次poll之间允许的最大时间差，超过会被剔出消费组
 */
#define MAX_POLL_INTERVAL_MS 30000000

/**
 * @brief 队列最大字节数(KB)
 * 消费者本地队列允许的最大字节数
 */
#define QUEUED_MAX_MESSAGES_KBYTES 1048576

/**
 * @brief 消费者最大重连间隔(ms)
 * 连接失败后，尝试重新连接的最大等待时间
 */
#define CONSUMER_RECONNECT_BACKOFF_MAX_MS 60000

/**
 * @brief 消费者初始重连间隔(ms)
 * 连接失败后，首次尝试重新连接的等待时间
 */
#define CONSUMER_RECONNECT_BACKOFF_MS 5000

/**
 * @brief 获取错误回退时间(ms)
 * 获取操作失败后的等待时间
 */
#define FETCH_ERROR_BACKOFF_MS 5000

/**
 * @brief 统计间隔(ms)
 * 生成内部统计数据的时间间隔
 */
#define STATISTICS_INTERVAL_MS 30000

/**
 * @brief 分区分配策略
 * 指定消费者组内分区分配方式，可选:"range"、"roundrobin"等
 */
#define PARTITION_ASSIGNMENT_STRATEGY "roundrobin"

/**
 * @brief 单次获取最大记录数
 * 单次调用poll最多返回的记录数
 */
#define MAX_POLL_RECORDS 10000

/* ================= 实现相关配置 ================= */

/* ================= 固定线程池配置 ================= */

/**
 * @brief 固定线程池总线程数
 * 32个主题 × 200个分区 = 6400个固定工作线程
 * 每个主题分区对应一个固定线程，避免动态创建/销毁线程的开销
 */
#define FIXED_THREAD_POOL_SIZE (TOPIC_COUNT * PARTITION_COUNT)

#endif // KAFKA_CONFIG_H