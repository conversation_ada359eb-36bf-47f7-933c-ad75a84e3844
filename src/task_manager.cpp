#include "exporter.h"
#include "mysql_pool.h"  // 添加MySQL连接池头文件
#include <fstream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sys/stat.h>  // 添加stat头文件
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <unordered_set>
#include "nlohmann_json.hpp" // 添加JSON库

// 声明外部函数
extern bool update_device_info_status(const std::string &chip_id, const std::string &datetime,
                                      int status, uint64_t processed_size,
                                      const std::string &actual_checksum, const std::string &error_message);

// 声明外部变量
extern AppConfig app_config; // 在exporter.cpp中定义

// 使用nlohmann/json库
using json = nlohmann::json;

// 计算简单的校验和（与producer.cpp中的计算方法相同）
uint32_t calculate_checksum(const uint8_t *data, size_t len)
{
    uint32_t checksum = 0;

    for(size_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }

    return checksum;
}

// MD5辅助函数
// 初始化MD5上下文
void init_md5_context(MD5_CTX *context)
{
    MD5_Init(context);
}

// 更新MD5上下文
void update_md5_context(MD5_CTX *context, const unsigned char *data, size_t length)
{
    MD5_Update(context, data, length);
}

// 完成MD5计算并返回十六进制字符串
std::string finalize_md5_context(MD5_CTX *context)
{
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5_CTX context_copy = *context; // 复制上下文，避免修改原始上下文
    MD5_Final(digest, &context_copy);
    std::stringstream ss;

    for(int i = 0; i < MD5_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)digest[i];
    }

    return ss.str();
}

// 计算消息指纹 - 使用消息内容和序列号的组合
std::string calculate_message_fingerprint(const std::string &payload, uint64_t sequence_number,
        const std::string &chip_id, const std::string &datetime)
{
    // 创建SHA256上下文
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    // 更新上下文 - 添加序列号
    std::string seq_str = std::to_string(sequence_number);
    SHA256_Update(&sha256, seq_str.c_str(), seq_str.length());
    // 更新上下文 - 添加芯片ID
    SHA256_Update(&sha256, chip_id.c_str(), chip_id.length());
    // 更新上下文 - 添加日期时间
    SHA256_Update(&sha256, datetime.c_str(), datetime.length());
    // 更新上下文 - 添加负载数据
    SHA256_Update(&sha256, payload.c_str(), payload.length());
    // 完成计算
    SHA256_Final(hash, &sha256);
    // 转换为十六进制字符串
    std::stringstream ss;

    for(int i = 0; i < SHA256_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
    }

    return ss.str();
}

// 按序列号排序所有消息，并计算MD5
std::string calculate_md5_from_messages(const std::map<uint64_t, MessageData> &sorted_messages)
{
    MD5_CTX context;
    init_md5_context(&context);
    // 记录消息数量和总大小，用于调试
    size_t message_count = sorted_messages.size();
    size_t total_size = 0;
    // 检查序列号是否连续
    uint64_t expected_seq = 0;
    bool first_message = true;
    bool sequence_gap = false;

    // 遍历排序后的消息集合，按序列号顺序计算MD5
    for(const auto &pair : sorted_messages)
    {
        uint64_t seq = pair.first;
        const MessageData &data = pair.second;

        // 检查序列号是否连续
        if(first_message)
        {
            expected_seq = seq;
            first_message = false;
        }
        else if(seq != expected_seq)
        {
            log_message(LOG_WARN, "MD5计算中发现序列号不连续: 期望 " + std::to_string(expected_seq) +
                        ", 实际 " + std::to_string(seq));
            sequence_gap = true;
        }

        expected_seq = seq + 1;
        // 更新MD5上下文
        update_md5_context(&context,
                           reinterpret_cast<const unsigned char *>(data.payload.data()),
                           data.payload.size());
        total_size += data.payload.size();
    }

    // 记录MD5计算的统计信息
    log_message(LOG_INFO, "MD5计算统计: 消息数量=" + std::to_string(message_count) +
                ", 总大小=" + std::to_string(total_size) + " 字节" +
                (sequence_gap ? ", 存在序列号不连续" : ", 序列号连续"));
    return finalize_md5_context(&context);
}

// 使用nlohmann/json库替换SimpleJson
namespace JsonUtils
{
    // 将任务状态保存为JSON格式字符串
    std::string serializeState(const std::map<std::string, FileProcessingState> &states)
    {
        json tasks_array = json::array();

        for(const auto &pair : states)
        {
            const FileProcessingState &state = pair.second;
            json task_obj =
            {
                {"chip_id", state.chip_id},
                {"datetime", state.datetime},
                {"filename", state.filename},
                {"expected_size", state.expected_size},
                {"processed_size", state.processed_size},
                {"min_sequence", state.min_sequence},
                {"max_sequence", state.max_sequence},
                {"expected_messages", state.expected_messages},
                {"actual_messages", state.actual_messages},
                {"checksum", state.checksum},
                {"start_time", state.start_time},
                {"last_update", state.last_update},
                {"status", static_cast<int>(state.status)},
                {"retry_count", state.retry_count},
                {"error_message", state.error_message}
            };
            tasks_array.push_back(task_obj);
        }

        json root = {{"tasks", tasks_array}};
        return root.dump(2); // 使用2个空格缩进
    }

    // 从JSON字符串解析任务状态
    bool parseState(const std::string &json_str, std::map<std::string, FileProcessingState> &states)
    {
        try
        {
            json root = json::parse(json_str);

            if(!root.contains("tasks") || !root["tasks"].is_array())
            {
                log_message(LOG_ERROR, "无效的JSON格式: 缺少tasks数组");
                return false;
            }

            states.clear();

            for(const auto &task_obj : root["tasks"])
            {
                FileProcessingState state;
                state.chip_id = task_obj["chip_id"];
                state.datetime = task_obj["datetime"];
                state.filename = task_obj["filename"];
                state.expected_size = task_obj["expected_size"];
                state.processed_size = task_obj["processed_size"];
                state.min_sequence = task_obj["min_sequence"];
                state.max_sequence = task_obj["max_sequence"];
                state.expected_messages = task_obj["expected_messages"];
                state.actual_messages = task_obj["actual_messages"];
                state.checksum = task_obj["checksum"];
                state.start_time = task_obj["start_time"];
                state.last_update = task_obj["last_update"];
                state.status = static_cast<TaskStatus>(task_obj["status"].get<int>());
                state.retry_count = task_obj["retry_count"];
                state.error_message = task_obj["error_message"];
                std::string task_key = state.chip_id + "_" + state.datetime;
                states[task_key] = state;
            }

            return true;
        }
        catch(const std::exception &e)
        {
            log_message(LOG_ERROR, std::string("解析状态文件失败: ") + e.what());
            return false;
        }
    }
}

// 任务管理器实现
TaskManager::TaskManager(const AppConfig &config)
    : config(config), running(true), last_status_report_time(time(nullptr)),
      last_retry_check_time(time(nullptr))
{
}

TaskManager::~TaskManager()
{
    stopAllTasks();
}

// 生成任务键
std::string TaskManager::generateTaskKey(const std::string &chip_id, const std::string &datetime)
{
    return chip_id + "_" + datetime;
}

// 初始化任务管理器
bool TaskManager::initialize()
{
    // 启动任务调度线程
    task_dispatcher_thread = std::thread(&TaskManager::taskDispatcherFunc, this);
    // 启动状态报告线程
    status_reporter_thread = std::thread(&TaskManager::statusReporterFunc, this);
    // 启动重处理失败任务线程
    retry_failed_tasks_thread = std::thread(&TaskManager::retryFailedTasksFunc, this);
    return true;
}

// 添加任务
bool TaskManager::addTask(const binary_stream::DeviceInfo &info)
{
    std::string task_key = generateTaskKey(info.chip_id(), info.datetime());
    // 检查任务是否已存在
    {
        std::lock_guard<std::mutex> lock(tasks_mutex);

        if(active_tasks.find(task_key) != active_tasks.end())
        {
            // 任务已存在且活动中
            log_message(LOG_INFO, "任务已存在: " + task_key);
            return false;
        }
    }
    {
        std::lock_guard<std::mutex> lock(state_mutex);
        auto it = completed_tasks.find(task_key);

        if(it != completed_tasks.end() && it->second.status == TASK_COMPLETED)
        {
            // 任务已完成，但要求重新获取
            log_message(LOG_INFO, "任务 " + task_key + " 已完成，将重新执行");
            completed_tasks.erase(it);
        }
    }
    // 创建新任务
    auto task = std::make_shared<DataRetrievalTask>();
    task->chip_id = info.chip_id();
    task->datetime = info.datetime();
    task->filename = info.log_file_name();
    task->file_size = info.log_file_size();
    task->checksum = info.file_checksum();
    task->topic = get_topic_for_chip_id(info.chip_id());
    task->active = false;
    // 设置输出路径
    task->output_path = config.output_dir + "/" + info.chip_id() + "/" + info.log_file_name();
    // 初始化状态
    task->state.chip_id = info.chip_id();
    task->state.datetime = info.datetime();
    task->state.filename = info.log_file_name();
    task->state.expected_size = info.log_file_size();
    task->state.checksum = task->checksum;
    task->state.status = TASK_PENDING;
    task->state.start_time = time(nullptr);
    task->state.last_update = time(nullptr);
    // 添加到队列
    {
        std::lock_guard<std::mutex> lock(queue_mutex);
        task_queue.push(task);
    }
    // 通知调度器
    queue_cv.notify_one();
    log_message(LOG_INFO, "已添加任务: " + task_key + ", 文件大小: " + std::to_string(
                                task->file_size) + " 字节");
    return true;
}

// 获取任务状态
FileProcessingState TaskManager::getTaskState(const std::string &chip_id, const std::string &datetime)
{
    std::string task_key = generateTaskKey(chip_id, datetime);
    // 先检查活动任务
    {
        std::lock_guard<std::mutex> lock(tasks_mutex);
        auto it = active_tasks.find(task_key);

        if(it != active_tasks.end())
        {
            return it->second->state;
        }
    }
    // 再检查已完成任务
    {
        std::lock_guard<std::mutex> lock(state_mutex);
        auto it = completed_tasks.find(task_key);

        if(it != completed_tasks.end())
        {
            return it->second;
        }
    }
    // 未找到任务
    FileProcessingState empty_state;
    empty_state.chip_id = chip_id;
    empty_state.datetime = datetime;
    empty_state.status = TASK_PENDING;
    return empty_state;
}

// 获取活动任务数
int TaskManager::getActiveTaskCount() const
{
    std::lock_guard<std::mutex> lock(tasks_mutex);
    return active_tasks.size();
}

// 保存状态
bool TaskManager::saveState() const
{
    // 使用简单JSON保存状态
    std::map<std::string, FileProcessingState> all_states;
    // 添加活动任务
    {
        std::lock_guard<std::mutex> lock(tasks_mutex);

        for(const auto &pair : active_tasks)
        {
            all_states[pair.first] = pair.second->state;
        }
    }
    // 添加已完成任务
    {
        std::lock_guard<std::mutex> lock(state_mutex);

        for(const auto &pair : completed_tasks)
        {
            all_states[pair.first] = pair.second;
        }
    }

    // 写入文件
    try
    {
        std::ofstream file(config.state_file);

        if(!file.is_open())
        {
            log_message(LOG_ERROR, "无法打开状态文件: " + config.state_file);
            return false;
        }

        file << JsonUtils::serializeState(all_states);
        file.close();
        return true;
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, "保存状态文件失败: " + std::string(e.what()));
        return false;
    }
}

// 加载状态
bool TaskManager::loadState()
{
    try
    {
        std::ifstream file(config.state_file);

        if(!file.is_open())
        {
            log_message(LOG_WARN, "无法打开状态文件: " + config.state_file);
            return false;
        }

        std::stringstream buffer;
        buffer << file.rdbuf();
        file.close();
        // 解析JSON
        std::map<std::string, FileProcessingState> loaded_states;

        if(!JsonUtils::parseState(buffer.str(), loaded_states))
        {
            log_message(LOG_ERROR, "解析状态文件失败");
            return false;
        }

        // 处理加载的任务
        {
            std::lock_guard<std::mutex> lock(state_mutex);

            for(const auto &pair : loaded_states)
            {
                std::string task_key = pair.first;
                FileProcessingState state = pair.second;

                if(state.status == TASK_COMPLETED)
                {
                    // 完成的任务直接添加到已完成列表
                    completed_tasks[task_key] = state;
                }
                else
                {
                    // 未完成的任务转换为新任务
                    auto task = std::make_shared<DataRetrievalTask>();
                    task->chip_id = state.chip_id;
                    task->datetime = state.datetime;
                    task->filename = state.filename;
                    task->file_size = state.expected_size;
                    task->checksum = state.checksum;
                    task->topic = get_topic_for_chip_id(task->chip_id);
                    task->active = false;
                    // 设置输出路径
                    task->output_path = config.output_dir + "/" + task->chip_id + "/" + task->filename;
                    // 初始化状态
                    task->state = state;
                    task->state.status = TASK_PENDING; // 重置为等待状态
                    // 添加到队列
                    std::lock_guard<std::mutex> queue_lock(queue_mutex);
                    task_queue.push(task);
                }
            }
        }
        return true;
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, "加载状态文件失败: " + std::string(e.what()));
        return false;
    }
}

// 停止所有任务
void TaskManager::stopAllTasks()
{
    running = false;
    // 首先停止所有活动任务
    {
        std::lock_guard<std::mutex> lock(tasks_mutex);

        for(auto &pair : active_tasks)
        {
            auto &task = pair.second;
            task->active = false;

            // 安全关闭消费者
            if(task->consumer)
            {
                try
                {
                    task->consumer->close();
                }
                catch(const std::exception &e)
                {
                    log_message(LOG_ERROR, "关闭消费者失败: " + std::string(e.what()));
                }
            }
        }
    }

    // 等待所有任务线程完成
    if(task_dispatcher_thread.joinable())
    {
        task_dispatcher_thread.join();
    }

    // 等待状态报告线程完成
    if(status_reporter_thread.joinable())
    {
        status_reporter_thread.join();
    }

    // 等待重处理失败任务线程完成
    if(retry_failed_tasks_thread.joinable())
    {
        retry_failed_tasks_thread.join();
    }

    active_tasks.clear();
}

// 更新任务状态
void TaskManager::updateTaskState(const std::string &task_key, const FileProcessingState &state)
{
    std::lock_guard<std::mutex> lock(state_mutex);
    completed_tasks[task_key] = state;
}

// 任务调度器线程函数
void TaskManager::taskDispatcherFunc()
{
    while(running)
    {
        std::shared_ptr<DataRetrievalTask> next_task;
        // 获取下一个任务
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            queue_cv.wait(lock, [this] { return !running || !task_queue.empty(); });

            if(!running)
            {
                break;
            }

            // 检查是否达到最大并发任务数
            if(getActiveTaskCount() >= config.max_tasks)
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }

            if(!task_queue.empty())
            {
                next_task = task_queue.front();
                task_queue.pop();
            }
        }

        // 处理下一个任务
        if(next_task)
        {
            std::string task_key = generateTaskKey(next_task->chip_id, next_task->datetime);
            // 首先检查任务是否已经在活动列表中
            {
                std::lock_guard<std::mutex> lock(tasks_mutex);

                if(active_tasks.find(task_key) != active_tasks.end())
                {
                    log_message(LOG_WARN, "任务已在活动列表中，跳过: " + task_key);
                    continue;
                }

                // 添加到活动任务
                active_tasks[task_key] = next_task;
            }

            // 确保没有遗留的消费者
            if(next_task->consumer)
            {
                try
                {
                    next_task->consumer->close();
                    next_task->consumer.reset();
                }
                catch(...)
                {
                    // 忽略异常，确保创建新的消费者
                }
            }

            // 创建消费者前记录详细信息
            log_message(LOG_INFO, "正在为任务创建消费者: " + task_key + ", 主题: " + next_task->topic);
            // 创建消费者，传入主题和芯片ID
            next_task->consumer.reset(createConsumer(next_task->topic, next_task->chip_id));

            if(!next_task->consumer)
            {
                std::string error_message = "无法创建消费者";
                log_message(LOG_ERROR, error_message + ": " + task_key);
                // 更新任务状态
                next_task->state.status = TASK_FAILED;
                next_task->state.error_message = error_message;
                updateTaskState(task_key, next_task->state);

                // 更新MySQL数据库中的设备信息状态
                if(app_config.use_mysql)
                {
                    update_device_info_status(
                                    next_task->chip_id,
                                    next_task->datetime,
                                    3, // 3表示处理失败
                                    0, // 处理大小为0
                                    "", // 没有校验和
                                    error_message
                    );
                }

                // 从活动任务中移除
                std::lock_guard<std::mutex> lock(tasks_mutex);
                active_tasks.erase(task_key);
                continue;
            }

            // 确保输出目录存在
            if(!ensureOutputDir(next_task->chip_id))
            {
                std::string error_message = "无法创建输出目录";
                log_message(LOG_ERROR, error_message + ": " + next_task->chip_id);
                // 更新任务状态
                next_task->state.status = TASK_FAILED;
                next_task->state.error_message = error_message;
                updateTaskState(task_key, next_task->state);

                // 更新MySQL数据库中的设备信息状态
                if(app_config.use_mysql)
                {
                    update_device_info_status(
                                    next_task->chip_id,
                                    next_task->datetime,
                                    3, // 3表示处理失败
                                    0, // 处理大小为0
                                    "", // 没有校验和
                                    error_message
                    );
                }

                // 从活动任务中移除
                std::lock_guard<std::mutex> lock(tasks_mutex);
                active_tasks.erase(task_key);
                continue;
            }

            // 检查目录访问权限
            if(access((config.output_dir + "/" + next_task->chip_id).c_str(), W_OK) != 0)
            {
                log_message(LOG_ERROR, "输出目录没有写入权限: " + config.output_dir + "/" + next_task->chip_id);
                log_message(LOG_INFO, "尝试修复权限问题...");
                // 尝试修复权限
                chmod((config.output_dir + "/" + next_task->chip_id).c_str(), 0755);
            }

            // 启动处理线程
            next_task->active = true;
            next_task->thread = std::thread(&TaskManager::taskProcessingFunc, this, next_task);
            // 将线程设为分离状态，这样它可以自行清理资源
            next_task->thread.detach();
            log_message(LOG_INFO, "已启动任务: " + task_key);
        }
    }
}

// 确保输出目录存在
bool TaskManager::ensureOutputDir(const std::string &chip_id)
{
    std::string dir_path = config.output_dir;

    if(!chip_id.empty())
    {
        dir_path += "/" + chip_id;
    }

    // 创建目录
    if(access(dir_path.c_str(), F_OK) != 0)
    {
        if(mkdir(dir_path.c_str(), 0755) != 0 && errno != EEXIST)
        {
            log_message(LOG_ERROR, "无法创建目录: " + dir_path + ", 错误: " + strerror(errno));
            return false;
        }
    }

    return true;
}

// 创建Kafka消费者
RdKafka::KafkaConsumer *TaskManager::createConsumer(const std::string &topic, const std::string &chip_id)
{
    log_message(LOG_INFO, "开始创建Kafka消费者，主题: " + topic);
    std::string errstr;
    RdKafka::Conf *conf = RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL);
    // 设置Kafka配置
    log_message(LOG_DEBUG, "设置Kafka配置: bootstrap.servers=" + config.kafka_brokers);

    if(conf->set("bootstrap.servers", config.kafka_brokers, errstr) != RdKafka::Conf::CONF_OK)
    {
        log_message(LOG_ERROR, "设置bootstrap.servers失败: " + errstr);
    }

    // 使用全局固定的消费者组，让Kafka自动管理分区分配和offset
    // 这是最佳实践：避免重复消费，正确管理offset，支持水平扩展
    std::string fixed_group_id = std::string(CONSUMER_GROUP_ID) + "_global";
    log_message(LOG_DEBUG, "设置Kafka配置: group.id=" + fixed_group_id);

    if(conf->set("group.id", fixed_group_id.c_str(), errstr) != RdKafka::Conf::CONF_OK)
    {
        log_message(LOG_ERROR, "设置group.id失败: " + errstr);
    }

    // 设置更多的配置，并记录日志
    std::vector<std::pair<std::string, std::string>> configs =
    {
        {"auto.offset.reset", "earliest"},
        {"enable.auto.commit", "false"}, // 禁用自动提交，确保数据完整性
        {"enable.auto.offset.store", "false"}, // 禁用自动offset存储，手动控制
        {"session.timeout.ms", "30000"},
        {"max.poll.interval.ms", "600000"}, // 增加轮询间隔，防止消费者被踢出组
        {"heartbeat.interval.ms", "10000"}, // 增加心跳间隔
        {"fetch.wait.max.ms", "500"}, // 减少等待时间，更频繁地检查消息
        {"fetch.error.backoff.ms", "500"}, // 减少错误回退时间
        {"coordinator.query.interval.ms", "60000"}, // 减少协调器查询频率
        {"partition.assignment.strategy", "range,roundrobin"} // 使用range和roundrobin分区分配策略
    };

    for(const auto &cfg : configs)
    {
        log_message(LOG_DEBUG, "设置Kafka配置: " + cfg.first + "=" + cfg.second);

        if(conf->set(cfg.first, cfg.second, errstr) != RdKafka::Conf::CONF_OK)
        {
            log_message(LOG_ERROR, "设置" + cfg.first + "失败: " + errstr);
        }
    }

    // 创建消费者
    log_message(LOG_INFO, "创建Kafka消费者实例...");
    RdKafka::KafkaConsumer *consumer = RdKafka::KafkaConsumer::create(conf, errstr);

    if(!consumer)
    {
        log_message(LOG_ERROR, "无法创建Kafka消费者: " + errstr);
        delete conf;
        return nullptr;
    }

    // 获取消费者元数据
    log_message(LOG_INFO, "获取Kafka集群元数据...");
    RdKafka::Metadata *metadata;
    RdKafka::ErrorCode meta_err = consumer->metadata(true, nullptr, &metadata, 5000);

    if(meta_err == RdKafka::ERR_NO_ERROR)
    {
        log_message(LOG_INFO, "Kafka集群: " + std::string(metadata->orig_broker_name()) +
                    " (ID: " + std::to_string(metadata->orig_broker_id()) + ")");
        // 检查主题是否存在
        bool topic_exists = false;

        for(const auto &topic_meta : *metadata->topics())
        {
            if(topic_meta->topic() == topic)
            {
                topic_exists = true;
                log_message(LOG_INFO, "主题 " + topic + " 存在，分区数: " +
                            std::to_string(topic_meta->partitions()->size()));
                break;
            }
        }

        if(!topic_exists)
        {
            log_message(LOG_WARN, "主题 " + topic + " 在集群中不存在，可能会导致消费失败");
        }

        delete metadata;
    }
    else
    {
        log_message(LOG_WARN, "获取元数据失败: " + std::string(RdKafka::err2str(meta_err)));
    }

    // 计算芯片ID对应的分区，手动分配特定分区
    // 这确保消费者能接收到对应芯片ID的消息
    int32_t partition = get_partition_for_chip_id(chip_id);
    std::vector<RdKafka::TopicPartition *> partitions;
    RdKafka::TopicPartition *tp = RdKafka::TopicPartition::create(topic, partition);
    partitions.push_back(tp);
    log_message(LOG_INFO, "手动分配分区: " + topic + "[" + std::to_string(partition) + "] (芯片ID: " + chip_id +
                ")");
    RdKafka::ErrorCode err = consumer->assign(partitions);

    // 释放TopicPartition对象
    for(auto &p : partitions)
    {
        delete p;
    }

    if(err != RdKafka::ERR_NO_ERROR)
    {
        log_message(LOG_ERROR, "无法分配分区 " + topic + "[" + std::to_string(partition) + "]: " + RdKafka::err2str(err));
        delete consumer;
        return nullptr;
    }

    // 获取分区分配
    std::vector<RdKafka::TopicPartition *> assigned_partitions;
    RdKafka::ErrorCode assign_err = consumer->assignment(assigned_partitions);

    if(assign_err == RdKafka::ERR_NO_ERROR)
    {
        std::stringstream ss;
        ss << "初始分区分配: ";

        for(const auto &tp : assigned_partitions)
        {
            ss << tp->topic() << "[" << tp->partition() << "] ";
            delete tp;
        }

        log_message(LOG_INFO, ss.str());
    }
    else
    {
        log_message(LOG_WARN, "获取分区分配失败: " + std::string(RdKafka::err2str(assign_err)));
    }

    log_message(LOG_INFO, "已成功创建并订阅主题: " + topic);
    return consumer;
}

// 任务处理线程函数
void TaskManager::taskProcessingFunc(std::shared_ptr<DataRetrievalTask> task)
{
    std::string task_key = generateTaskKey(task->chip_id, task->datetime);
    std::thread::id thread_id = std::this_thread::get_id();
    // 更新线程状态为工作中
    updateThreadStatus(thread_id, THREAD_WORKING, task_key);

    try
    {
        // 更新任务状态
        task->state.status = TASK_RUNNING;
        task->state.last_update = time(nullptr);

        // 初始化MD5上下文
        if(!task->md5_initialized)
        {
            init_md5_context(&task->md5_context);
            task->md5_initialized = true;
        }

        // 准备文件缓冲区和输出文件
        FileBuffer buffer;
        std::map<uint64_t, bool> received_sequences;
        // 增加一个有序的消息映射，按序列号排序所有接收到的消息
        std::map<uint64_t, MessageData> sorted_messages;
        // 打开输出文件
        std::ofstream output_file(task->output_path, std::ios::binary | std::ios::trunc);

        if(!output_file.is_open())
        {
            std::string error_message = "无法打开输出文件";
            log_message(LOG_ERROR, error_message + ": " + task->output_path);
            // 更新任务状态
            task->state.status = TASK_FAILED;
            task->state.error_message = error_message;
            updateTaskState(task_key, task->state);

            // 更新MySQL数据库中的设备信息状态
            if(app_config.use_mysql)
            {
                update_device_info_status(
                                task->chip_id,
                                task->datetime,
                                3, // 3表示处理失败
                                0, // 处理大小为0
                                "", // 没有校验和
                                error_message
                );
            }

            // 从活动任务中移除
            {
                std::lock_guard<std::mutex> lock(tasks_mutex);
                auto it = active_tasks.find(task_key);

                if(it != active_tasks.end())
                {
                    try
                    {
                        if(it->second && it->second->consumer)
                        {
                            it->second->consumer->close();
                            it->second->consumer.reset();
                        }
                    }
                    catch(...)
                    {
                        // 忽略异常
                    }

                    active_tasks.erase(it);
                }
            }
            task->active = false;
            return;
        }

        // 确保文件打开成功后立即写入
        output_file << std::flush;
        time_t start_time = time(nullptr);
        uint64_t message_count = 0;
        uint64_t total_bytes = 0;
        bool finished = false;

        while(running && task->active && !finished)
        {
            // 更新线程活动时间，表明线程仍在工作
            time_t current_time = time(nullptr);
            time_t elapsed = current_time - start_time;
            updateThreadStatus(std::this_thread::get_id(), THREAD_WORKING,
                             "等待消息中: " + task_key + ", 已等待 " +
                             std::to_string(elapsed) + " 秒, 分配的分区: " +
                             task->topic + "[" + std::to_string(get_partition_for_chip_id(task->chip_id)) + "]");

            // 获取消息
            RdKafka::Message *msg = task->consumer->consume(POLL_TIMEOUT_MS);

            if(msg)
            {
                switch(msg->err())
                {
                    case RdKafka::ERR_NO_ERROR:
                        {
                            // 解析消息
                            binary_stream::StreamData stream_data;

                            if(stream_data.ParseFromArray(msg->payload(), static_cast<int>(msg->len())))
                            {
                                // 验证单个数据包的校验和
                                // 直接获取校验和，不需要检查是否存在
                                uint32_t received_checksum = stream_data.checksum();
                                uint32_t calculated_checksum = calculate_checksum(
                                        reinterpret_cast<const uint8_t *>(stream_data.payload().data()),
                                        stream_data.payload().size());

                                if(received_checksum != calculated_checksum)
                                {
                                    log_message(LOG_WARN, "数据包校验和不匹配: 序列号=" +
                                                std::to_string(stream_data.sequence_number()) +
                                                ", 期望=" + std::to_string(received_checksum) +
                                                ", 计算得到=" + std::to_string(calculated_checksum));
                                    // 严格校验数据完整性，跳过校验和不匹配的数据包
                                    log_message(LOG_ERROR, "跳过校验和不匹配的数据包，确保数据完整性");
                                    continue;
                                }

                                // 不再立即更新MD5，而是先排序再更新

                                // 验证芯片ID和日期时间
                                if(stream_data.chip_id() == task->chip_id && stream_data.datetime() == task->datetime)
                                {
                                    // 处理消息
                                    uint64_t seq_num = stream_data.sequence_number();
                                    std::string payload = stream_data.payload();
                                    // 增加任务的总处理消息计数
                                    task->total_processed_count++;

                                    // 清理过期的指纹记录 - 每10000条消息检查一次
                                    if(task->total_processed_count % 10000 == 0)
                                    {
                                        time_t now = time(nullptr);

                                        // 如果去重窗口超过1小时，清空指纹集合
                                        if(now - task->dedup_window_start > 3600)
                                        {
                                            log_message(LOG_INFO, "清理去重窗口: " + task_key +
                                                        ", 清除 " + std::to_string(task->message_fingerprints.size()) +
                                                        " 条指纹记录");
                                            task->message_fingerprints.clear();
                                            task->dedup_window_start = now;
                                        }
                                    }

                                    // 第一级去重：基于序列号
                                    bool is_duplicate = false;

                                    if(received_sequences.find(seq_num) != received_sequences.end())
                                    {
                                        task->seq_duplicate_count++;
                                        task->duplicate_count++;
                                        is_duplicate = true;

                                        // 只在特定情况下输出日志，减少日志数量
                                        if(task->duplicate_count == 1 ||
                                                seq_num != task->last_reported_duplicate_seq ||
                                                task->duplicate_count % 100 == 0)
                                        {
                                            log_message(LOG_WARN, "检测到序列号重复: " + task_key +
                                                        ", 序列号: " + std::to_string(seq_num) +
                                                        ", 累计重复: " + std::to_string(task->duplicate_count));
                                            task->last_reported_duplicate_seq = seq_num;
                                        }
                                    }
                                    else
                                    {
                                        // 第二级去重：基于消息内容指纹
                                        // 计算消息指纹
                                        std::string fingerprint = calculate_message_fingerprint(
                                                payload, seq_num, stream_data.chip_id(), stream_data.datetime());

                                        // 检查指纹是否已存在
                                        if(task->message_fingerprints.find(fingerprint) != task->message_fingerprints.end())
                                        {
                                            task->content_duplicate_count++;
                                            task->duplicate_count++;
                                            is_duplicate = true;
                                            // 记录内容重复但序列号不同的情况
                                            log_message(LOG_WARN, "检测到内容重复: " + task_key +
                                                        ", 序列号: " + std::to_string(seq_num) +
                                                        ", 指纹: " + fingerprint.substr(0, 16) + "...");
                                        }
                                        else
                                        {
                                            // 添加指纹到集合
                                            task->message_fingerprints.insert(fingerprint);
                                        }
                                    }

                                    // 如果是重复消息，跳过处理
                                    if(is_duplicate)
                                    {
                                        continue;
                                    }

                                    // 处理非重复消息
                                    MessageData data;
                                    data.chip_id = stream_data.chip_id();
                                    data.datetime = stream_data.datetime();
                                    data.sequence_number = seq_num;
                                    data.payload = payload;
                                    data.timestamp = stream_data.timestamp();
                                    // 保存消息指纹
                                    data.fingerprint = calculate_message_fingerprint(
                                                                       payload, seq_num, data.chip_id, data.datetime);
                                    // 添加到缓冲区
                                    buffer.messages.push_back(data);
                                    buffer.last_update = time(nullptr);
                                    // 保存到有序映射中
                                    sorted_messages[seq_num] = data;
                                    // 标记序列号为已接收
                                    received_sequences[seq_num] = true;
                                    // 更新统计信息
                                    message_count++;
                                    total_bytes += data.payload.size();
                                    // 更新线程统计信息
                                    updateThreadStats(thread_id, data.payload.size(), 1);

                                    // 存储当前消息的offset，但不立即提交
                                    if(msg->offset_store() != nullptr)
                                    {
                                        log_message(LOG_DEBUG, "存储offset失败: " + task_key +
                                                    ", offset: " + std::to_string(msg->offset()));
                                    }

                                    // 更新序列号范围
                                    if(task->state.min_sequence == 0 || data.sequence_number < task->state.min_sequence)
                                    {
                                        task->state.min_sequence = data.sequence_number;
                                    }

                                    if(data.sequence_number > task->state.max_sequence)
                                    {
                                        task->state.max_sequence = data.sequence_number;
                                    }

                                    // 检查是否是最后一帧
                                    if(stream_data.is_last_frame())
                                    {
                                        log_message(LOG_INFO, "收到最后一帧: " + task_key + ", 序列号: " +
                                                    std::to_string(data.sequence_number));
                                        // 强制刷新缓冲区，确保最后一帧写入文件
                                        finished = true;
                                    }

                                    // 定期刷新缓冲区
                                    if(buffer.messages.size() >= 500 ||
                                            (time(nullptr) - buffer.last_update >= 3) ||
                                            finished)
                                    {
                                        // 排序消息
                                        if(config.sort_messages)
                                        {
                                            std::sort(buffer.messages.begin(), buffer.messages.end());
                                        }

                                        // 写入文件
                                        for(const auto &msg_data : buffer.messages)
                                        {
                                            output_file.write(msg_data.payload.data(), msg_data.payload.size());
                                        }

                                        // 确保数据写入磁盘
                                        output_file.flush();
                                        // 清空缓冲区
                                        buffer.messages.clear();
                                        // 更新任务状态
                                        task->state.processed_size = total_bytes;
                                        task->state.actual_messages = message_count;
                                        task->state.last_update = time(nullptr);

                                        // 检查是否完成
                                        if(total_bytes >= task->file_size)
                                        {
                                            // 构建详细的去重统计信息
                                            std::string dedup_stats;

                                            if(task->duplicate_count > 0)
                                            {
                                                dedup_stats = ", 跳过 " + std::to_string(task->duplicate_count) + " 条重复消息";

                                                // 添加详细的去重统计
                                                if(task->seq_duplicate_count > 0 || task->content_duplicate_count > 0)
                                                {
                                                    dedup_stats += " (序列号重复: " + std::to_string(task->seq_duplicate_count) +
                                                                   ", 内容重复: " + std::to_string(task->content_duplicate_count) + ")";
                                                }

                                                // 计算重复率
                                                double duplicate_rate = 0.0;

                                                if(task->total_processed_count > 0)
                                                {
                                                    duplicate_rate = (task->duplicate_count * 100.0) / task->total_processed_count;
                                                    dedup_stats += ", 重复率: " + std::to_string(duplicate_rate) + "%";
                                                }
                                            }

                                            log_message(LOG_INFO, "任务已完成: " + task_key +
                                                        ", 处理了 " + std::to_string(message_count) + " 条消息, " +
                                                        std::to_string(total_bytes) + " 字节" + dedup_stats);
                                            finished = true;
                                            break;
                                        }
                                    }
                                }
                            }
                            else
                            {
                                log_message(LOG_ERROR, "消息解析错误");
                                recordThreadError(thread_id, "消息解析错误");
                            }

                            break;
                        }

                    case RdKafka::ERR__PARTITION_EOF:
                        // 已到达分区末尾，继续等待
                        break;

                    case RdKafka::ERR__TIMED_OUT:
                        // 超时，正常情况
                        {
                            // 检查是否已超时
                            time_t now = time(nullptr);
                            time_t elapsed = now - start_time;

                            // 每30秒记录一次等待状态，使用任务对象中的字段避免重复打印
                            if(elapsed % 30 == 0 && elapsed > 0 && message_count == 0 &&
                                    now != task->last_wait_log_time) // 确保同一秒内不会重复打印
                            {
                                // 获取消费者分配的分区信息
                                std::vector<RdKafka::TopicPartition *> partitions;
                                RdKafka::ErrorCode err = task->consumer->assignment(partitions);
                                std::stringstream ss;
                                ss << "等待消息中: " << task_key << ", 已等待 " << elapsed << " 秒";

                                if(err == RdKafka::ERR_NO_ERROR)
                                {
                                    ss << ", 分配的分区: ";

                                    for(const auto &tp : partitions)
                                    {
                                        ss << tp->topic() << "[" << tp->partition() << "] ";
                                        delete tp;
                                    }
                                }
                                else
                                {
                                    ss << ", 无法获取分区信息: " << RdKafka::err2str(err);
                                }

                                log_message(LOG_INFO, ss.str());
                                task->last_wait_log_time = now; // 更新任务的最后打印时间
                            }

                            // 超时时间从120秒增加到300秒（5分钟）
                            if(elapsed > 300 && message_count == 0)
                            {
                                log_message(LOG_WARN, "任务超时无消息: " + task_key + ", 已等待 " + std::to_string(elapsed) + " 秒");
                                // 尝试获取消费者元数据，帮助诊断问题
                                std::string metadata_str = "无法获取元数据";

                                try
                                {
                                    RdKafka::Metadata *metadata;
                                    RdKafka::ErrorCode err = task->consumer->metadata(true, nullptr, &metadata, 5000);

                                    if(err == RdKafka::ERR_NO_ERROR)
                                    {
                                        std::stringstream ss;
                                        ss << "Kafka集群: " << metadata->orig_broker_name() << " (" << metadata->orig_broker_id() << ")";
                                        ss << ", 主题数: " << metadata->topics()->size();
                                        metadata_str = ss.str();
                                        delete metadata;
                                    }
                                    else
                                    {
                                        metadata_str = "获取元数据失败: " + std::string(RdKafka::err2str(err));
                                    }
                                }
                                catch(const std::exception &e)
                                {
                                    metadata_str = "获取元数据异常: " + std::string(e.what());
                                }

                                log_message(LOG_INFO, "Kafka状态: " + metadata_str);
                                // 更新任务状态
                                task->state.status = TASK_RETRY;
                                std::string error_message = "获取消息超时";
                                task->state.error_message = error_message;
                                task->state.retry_count++;

                                // 确保安全关闭当前消费者
                                if(task->consumer)
                                {
                                    try
                                    {
                                        task->consumer->close();  // 先关闭消费者
                                        task->consumer.reset();   // 释放消费者资源
                                    }
                                    catch(const std::exception &e)
                                    {
                                        log_message(LOG_ERROR, "关闭消费者失败: " + std::string(e.what()));
                                    }
                                }

                                // 从活动任务中移除，再加入重试队列
                                {
                                    std::lock_guard<std::mutex> lock(tasks_mutex);
                                    active_tasks.erase(task_key);
                                }

                                if(task->state.retry_count >= config.retry_count)
                                {
                                    task->state.status = TASK_FAILED;
                                    error_message = "任务重试次数已达上限，标记为失败";
                                    task->state.error_message = error_message;
                                    log_message(LOG_ERROR, error_message + ": " + task_key);

                                    // 更新MySQL数据库中的设备信息状态
                                    if(app_config.use_mysql)
                                    {
                                        update_device_info_status(
                                                        task->chip_id,
                                                        task->datetime,
                                                        3, // 3表示处理失败
                                                        0, // 处理大小为0
                                                        "", // 没有校验和
                                                        error_message
                                        );
                                    }
                                }
                                else
                                {
                                    // 设置为非活动状态，等待重新入队
                                    task->active = false;
                                    // 重新入队等待重试
                                    {
                                        std::lock_guard<std::mutex> lock(queue_mutex);
                                        task_queue.push(task);
                                        queue_cv.notify_one();
                                    }
                                    log_message(LOG_INFO, "任务将重试: " + task_key + ", 重试次数: " +
                                                std::to_string(task->state.retry_count));
                                }

                                // 等待当前线程自然退出
                                return;
                            }
                        }
                        break;

                    default:
                        log_message(LOG_ERROR, "消费错误: " + msg->errstr());
                        break;
                }

                delete msg;
            }

            // 检查是否需要显示进度
            // 条件：1. 消息数量是5000的倍数 2. 距离上次打印至少5秒 3. 消息数量有变化
            time_t now = time(nullptr);

            if(config.verbose_level >= 2 && message_count > 0 &&
                    (message_count % 5000 == 0 || message_count == task->state.expected_messages) &&
                    (now - task->last_progress_log_time >= 5) &&
                    (message_count != task->last_progress_message_count))
            {
                double progress = (total_bytes * 100.0) / task->file_size;
                log_message(LOG_INFO, "任务进度: " + task_key + ": " +
                            std::to_string(static_cast<int>(progress)) + "%, " +
                            std::to_string(message_count) + " 条消息");
                // 更新最后打印时间和消息计数
                task->last_progress_log_time = now;
                task->last_progress_message_count = message_count;
            }
        }

        // 刷新剩余缓冲区
        if(!buffer.messages.empty())
        {
            // 排序消息
            if(config.sort_messages)
            {
                std::sort(buffer.messages.begin(), buffer.messages.end());
            }

            // 写入文件
            for(const auto &msg_data : buffer.messages)
            {
                output_file.write(msg_data.payload.data(), msg_data.payload.size());
            }

            // 确保数据写入磁盘
            output_file.flush();
            // 清空缓冲区
            buffer.messages.clear();
        }

        // 关闭文件
        output_file.close();
        // 检查文件是否成功写入
        struct stat file_stat;

        if(stat(task->output_path.c_str(), &file_stat) != 0 || file_stat.st_size == 0)
        {
            log_message(LOG_ERROR, "文件写入失败或大小为0: " + task->output_path);

            // 如果文件为空，将缓存的数据全部写入
            if(total_bytes > 0 && file_stat.st_size == 0)
            {
                log_message(LOG_INFO, "尝试重新写入文件...");
                // 尝试重新打开并写入所有数据
                std::ofstream recovery_file(task->output_path, std::ios::binary | std::ios::trunc);

                if(recovery_file.is_open())
                {
                    // 写入已处理的数据
                    if(!received_sequences.empty())
                    {
                        log_message(LOG_WARN, "检测到" + std::to_string(received_sequences.size()) + "条消息数据，但文件为空");
                        log_message(LOG_WARN, "文件恢复困难，请检查文件系统权限和磁盘空间");
                    }

                    recovery_file.close();
                }
            }
        }

        // 根据排序后的消息重新计算MD5校验和
        std::string calculated_md5 = calculate_md5_from_messages(sorted_messages);

        // 更新基本任务状态，无论成功失败
        if(task->state.status != TASK_FAILED && task->state.status != TASK_RETRY)
        {
            task->state.status = TASK_COMPLETED;
        }

        task->state.processed_size = total_bytes;
        task->state.actual_messages = message_count;
        // 定义处理状态和错误消息，用于更新MySQL
        int process_status = 1; // 默认为1表示成功
        std::string error_msg = "";

        // 校验和验证
        if(!task->checksum.empty())
        {
            // 记录更多关于校验和的信息
            log_message(LOG_INFO, "校验和验证: 文件=" + task->filename +
                        ", 大小=" + std::to_string(task->file_size) + " 字节" +
                        ", 实际处理=" + std::to_string(total_bytes) + " 字节" +
                        ", 消息数=" + std::to_string(message_count) +
                        ", 期望校验和=" + task->checksum);

            if(calculated_md5 == task->checksum)
            {
                log_message(LOG_INFO, "文件校验和匹配: " + calculated_md5);
                // 设置任务状态为已完成
                task->state.status = TASK_COMPLETED;
                process_status = 1; // 成功
            }
            else
            {
                // 记录更详细的错误信息
                log_message(LOG_ERROR, "文件校验和不匹配: 期望 " + task->checksum +
                            ", 计算得到 " + calculated_md5);
                // 检查是否有缺失帧
                bool missing_frames = false;

                for(uint64_t seq = task->state.min_sequence; seq <= task->state.max_sequence; seq++)
                {
                    if(sorted_messages.find(seq) == sorted_messages.end())
                    {
                        log_message(LOG_WARN, "校验和不匹配可能原因: 缺失序列号 " + std::to_string(seq));
                        missing_frames = true;
                    }
                }

                if(!missing_frames)
                {
                    log_message(LOG_INFO, "校验和不匹配但序列号完整，可能是数据传输过程中发生了变化");
                }

                // 检查文件大小是否匹配
                if(total_bytes != task->file_size)
                {
                    // 计算差异百分比
                    double size_diff = std::abs(static_cast<double>(total_bytes) - static_cast<double>(task->file_size));
                    double diff_percent = (size_diff / task->file_size) * 100.0;
                    log_message(LOG_WARN, "校验和不匹配可能原因: 文件大小不匹配，期望 " +
                                std::to_string(task->file_size) + " 字节，实际 " +
                                std::to_string(total_bytes) + " 字节，差异 " +
                                std::to_string(static_cast<int64_t>(total_bytes) - static_cast<int64_t>(task->file_size)) +
                                " 字节 (" + std::to_string(diff_percent) + "%)");
                    // 严格校验数据完整性，不忽略任何校验和不匹配
                    log_message(LOG_ERROR, "文件大小不匹配，数据完整性验证失败");

                    // 如果有重复消息，记录为可能的原因
                    if(task->duplicate_count > 0)
                    {
                        log_message(LOG_WARN, "校验和不匹配可能与重复消息有关，共检测到 " +
                                    std::to_string(task->duplicate_count) + " 条重复消息");
                    }

                    // 检查消息数量是否与预期一致
                    if(message_count != task->state.expected_messages && task->state.expected_messages > 0)
                    {
                        log_message(LOG_WARN, "校验和不匹配可能原因: 消息数量不匹配，期望 " +
                                    std::to_string(task->state.expected_messages) + " 条，实际 " +
                                    std::to_string(message_count) + " 条");
                    }
                }

                task->state.status = TASK_COMPLETED; // 依然标记为完成，但记录错误
                task->state.error_message = "校验和不匹配";
                process_status = 2; // 校验和错误
                error_msg = "校验和不匹配: 期望 " + task->checksum + ", 计算得到 " + calculated_md5;
            }
        }
        else
        {
            log_message(LOG_WARN, "未提供文件校验和，无法验证完整性");
            // 没有校验和，依然标记为完成
            task->state.status = TASK_COMPLETED;
            process_status = 1; // 成功，但没有校验和
        }

        // 更新最后更新时间
        task->state.last_update = time(nullptr);

        // 更新完成状态
        try
        {
            updateTaskState(task_key, task->state);

            // 更新MySQL数据库中的设备信息状态
            if(app_config.use_mysql)
            {
                update_device_info_status(
                                task->chip_id,
                                task->datetime,
                                process_status,
                                total_bytes,
                                calculated_md5,
                                error_msg
                );
            }

            // 根据处理状态决定是否提交offset
            if(task->consumer)
            {
                if(process_status == 1)
                {
                    // 任务成功完成，提交offset
                    RdKafka::ErrorCode commit_err = task->consumer->commitSync();

                    if(commit_err != RdKafka::ERR_NO_ERROR)
                    {
                        log_message(LOG_WARN, "任务完成后提交offset失败: " + task_key +
                                    ", 错误: " + RdKafka::err2str(commit_err));
                    }
                    else
                    {
                        log_message(LOG_DEBUG, "任务成功完成，offset已提交: " + task_key);
                    }
                }
                else if(process_status == 2)
                {
                    // 校验和不匹配，但数据已接收完整，提交offset避免重复处理
                    RdKafka::ErrorCode commit_err = task->consumer->commitSync();

                    if(commit_err != RdKafka::ERR_NO_ERROR)
                    {
                        log_message(LOG_WARN, "校验和不匹配任务提交offset失败: " + task_key +
                                    ", 错误: " + RdKafka::err2str(commit_err));
                    }
                    else
                    {
                        log_message(LOG_INFO, "校验和不匹配但数据完整，offset已提交: " + task_key);
                    }
                }
                else
                {
                    // 任务失败，不提交offset，允许重新处理
                    log_message(LOG_INFO, "任务失败，不提交offset，允许重新处理: " + task_key);
                }
            }

            // 更新线程统计信息
            {
                std::lock_guard<std::mutex> lock(thread_stats_mutex);
                auto &stats = thread_stats[thread_id];
                stats.tasks_processed++;

                if(process_status == 1)
                {
                    stats.tasks_completed++;
                }
                else
                {
                    stats.tasks_failed++;
                }
            }
        }
        catch(const std::exception &e)
        {
            log_message(LOG_ERROR, "更新任务状态时出错: " + std::string(e.what()));
            recordThreadError(thread_id, "更新任务状态失败: " + std::string(e.what()));
        }

        // 检查是否有缺失帧
        if(task->state.status == TASK_COMPLETED)
        {
            // 检查序列号是否连续
            bool missing_sequences = false;

            for(uint64_t seq = task->state.min_sequence; seq <= task->state.max_sequence; seq++)
            {
                if(received_sequences.find(seq) == received_sequences.end())
                {
                    log_message(LOG_WARN, "缺失序列号: " + task_key + ", 序列号: " + std::to_string(seq));
                    missing_sequences = true;
                }
            }

            if(missing_sequences)
            {
                log_message(LOG_WARN, "文件中存在缺失帧: " + task_key);
            }
            else
            {
                log_message(LOG_INFO, "文件帧序列完整: " + task_key);
            }

            // 文件已全部接收完成
            // 构建详细的去重统计信息
            std::string dedup_stats;

            if(task->duplicate_count > 0)
            {
                dedup_stats = ", 跳过 " + std::to_string(task->duplicate_count) + " 条重复消息";

                // 添加详细的去重统计
                if(task->seq_duplicate_count > 0 || task->content_duplicate_count > 0)
                {
                    dedup_stats += " (序列号重复: " + std::to_string(task->seq_duplicate_count) +
                                   ", 内容重复: " + std::to_string(task->content_duplicate_count) + ")";
                }

                // 计算重复率
                double duplicate_rate = 0.0;

                if(task->total_processed_count > 0)
                {
                    duplicate_rate = (task->duplicate_count * 100.0) / task->total_processed_count;
                    dedup_stats += ", 重复率: " + std::to_string(duplicate_rate) + "%";
                }
            }

            log_message(LOG_INFO, "文件接收完成: " + task_key + ", 处理了 " +
                        std::to_string(task->state.actual_messages) + " 条消息, " +
                        std::to_string(task->state.processed_size) + " 字节" + dedup_stats);
        }

        // 从活动任务中移除
        {
            std::lock_guard<std::mutex> lock(tasks_mutex);
            auto it = active_tasks.find(task_key);

            if(it != active_tasks.end())
            {
                // 确保在移除任务前正确关闭消费者
                try
                {
                    if(it->second && it->second->consumer)
                    {
                        it->second->consumer->close();
                        it->second->consumer.reset();
                    }
                }
                catch(const std::exception &e)
                {
                    log_message(LOG_ERROR, "关闭消费者异常: " + std::string(e.what()));
                }

                active_tasks.erase(it);
            }
        }
        // 确保线程可以正常退出
        task->active = false;
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, "任务处理异常: " + task_key + ", " + e.what());
        // 记录线程错误
        recordThreadError(thread_id, "任务处理异常: " + std::string(e.what()));
        // 更新任务状态
        task->state.status = TASK_FAILED;
        std::string error_message = "处理异常: " + std::string(e.what());
        task->state.error_message = error_message;
        updateTaskState(task_key, task->state);

        // 更新MySQL数据库中的设备信息状态
        if(app_config.use_mysql)
        {
            update_device_info_status(
                            task->chip_id,
                            task->datetime,
                            3, // 3表示处理失败
                            0, // 处理大小为0
                            "", // 没有校验和
                            error_message
            );
        }

        // 从活动任务中移除
        {
            std::lock_guard<std::mutex> lock(tasks_mutex);
            auto it = active_tasks.find(task_key);

            if(it != active_tasks.end())
            {
                try
                {
                    if(it->second && it->second->consumer)
                    {
                        it->second->consumer->close();
                        it->second->consumer.reset();
                    }
                }
                catch(...)
                {
                    // 忽略异常
                }

                active_tasks.erase(it);
            }
        }
        // 确保线程可以正常退出
        task->active = false;
        // 在异常情况下也清理线程状态
        {
            std::lock_guard<std::mutex> lock(thread_stats_mutex);
            auto it = thread_stats.find(thread_id);

            if(it != thread_stats.end())
            {
                log_message(LOG_DEBUG, "异常退出时移除线程状态记录: " + it->second.thread_id);
                thread_stats.erase(it);
            }
        }
    }

    // 为防止线程问题，在函数结束前进行最后的清理
    try
    {
        if(task && task->consumer)
        {
            task->consumer.reset();
        }
    }
    catch(...)
    {
        // 忽略任何异常
    }

    // 线程即将结束，直接从状态记录中移除
    {
        std::lock_guard<std::mutex> lock(thread_stats_mutex);
        auto it = thread_stats.find(thread_id);

        if(it != thread_stats.end())
        {
            log_message(LOG_DEBUG, "移除已完成线程的状态记录: " + it->second.thread_id);
            thread_stats.erase(it);
        }
    }
    // 添加调试日志
    log_message(LOG_DEBUG, "任务处理线程结束: " + task_key + ", 线程ID: " + std::to_string(
                                std::hash<std::thread::id> {}(thread_id)));
}

// 线程状态管理方法实现

// 更新线程状态
void TaskManager::updateThreadStatus(std::thread::id thread_id, ThreadStatus status, const std::string &current_task)
{
    std::lock_guard<std::mutex> lock(thread_stats_mutex);
    auto &stats = thread_stats[thread_id];

    if(stats.thread_id.empty())
    {
        // 初始化新线程的统计信息
        std::stringstream ss;
        ss << thread_id;
        stats.thread_id = ss.str();
        stats.start_time = time(nullptr);
    }

    stats.status = status;
    stats.current_task = current_task;
    stats.last_activity_time = time(nullptr);
}

// 更新线程统计信息
void TaskManager::updateThreadStats(std::thread::id thread_id, uint64_t bytes_processed, uint64_t messages_processed)
{
    std::lock_guard<std::mutex> lock(thread_stats_mutex);
    auto &stats = thread_stats[thread_id];
    stats.bytes_processed += bytes_processed;
    stats.messages_processed += messages_processed;
    stats.last_activity_time = time(nullptr);
}

// 记录线程错误
void TaskManager::recordThreadError(std::thread::id thread_id, const std::string &error_msg)
{
    std::lock_guard<std::mutex> lock(thread_stats_mutex);
    auto &stats = thread_stats[thread_id];
    stats.errors_count++;
    stats.last_error = error_msg;
    stats.status = THREAD_ERROR;
    stats.last_activity_time = time(nullptr);
}

// 获取线程状态信息
std::vector<ThreadStats> TaskManager::getThreadStats() const
{
    std::lock_guard<std::mutex> lock(thread_stats_mutex);
    std::vector<ThreadStats> result;

    for(const auto &pair : thread_stats)
    {
        result.push_back(pair.second);
    }

    return result;
}

// 打印线程状态报告
void TaskManager::printThreadStatusReport()
{
    // 首先清理过期的线程状态
    cleanupExpiredThreads();
    auto stats = getThreadStats();
    time_t now = time(nullptr);
    log_message(LOG_INFO, "========== 线程状态报告 ==========");
    log_message(LOG_INFO, "报告时间: " + std::string(ctime(&now)));

    if(stats.empty())
    {
        log_message(LOG_INFO, "当前没有活动的工作线程");
        return;
    }

    // 统计总体信息
    uint64_t total_tasks_processed = 0;
    uint64_t total_tasks_completed = 0;
    uint64_t total_tasks_failed = 0;
    uint64_t total_bytes_processed = 0;
    uint64_t total_messages_processed = 0;
    uint64_t total_errors = 0;
    int idle_threads = 0;
    int working_threads = 0;
    int error_threads = 0;

    for(const auto &stat : stats)
    {
        total_tasks_processed += stat.tasks_processed;
        total_tasks_completed += stat.tasks_completed;
        total_tasks_failed += stat.tasks_failed;
        total_bytes_processed += stat.bytes_processed;
        total_messages_processed += stat.messages_processed;
        total_errors += stat.errors_count;

        switch(stat.status)
        {
            case THREAD_IDLE:
                idle_threads++;
                break;

            case THREAD_WORKING:
                working_threads++;
                break;

            case THREAD_ERROR:
                error_threads++;
                break;

            default:
                break;
        }
    }

    // 打印总体统计
    log_message(LOG_INFO, "总体统计:");
    log_message(LOG_INFO, "  活动线程数: " + std::to_string(stats.size()));
    log_message(LOG_INFO, "  空闲线程: " + std::to_string(idle_threads) +
                ", 工作线程: " + std::to_string(working_threads) +
                ", 错误线程: " + std::to_string(error_threads));
    log_message(LOG_INFO, "  总处理任务: " + std::to_string(total_tasks_processed));
    log_message(LOG_INFO, "  总完成任务: " + std::to_string(total_tasks_completed));
    log_message(LOG_INFO, "  总失败任务: " + std::to_string(total_tasks_failed));
    log_message(LOG_INFO, "  总处理字节: " + std::to_string(total_bytes_processed) + " (" +
                std::to_string(total_bytes_processed / 1024.0 / 1024.0) + " MB)");
    log_message(LOG_INFO, "  总处理消息: " + std::to_string(total_messages_processed));
    log_message(LOG_INFO, "  总错误数: " + std::to_string(total_errors));
    // 打印每个线程的详细信息
    log_message(LOG_INFO, "线程详细信息:");

    for(const auto &stat : stats)
    {
        std::string status_str;

        switch(stat.status)
        {
            case THREAD_IDLE:
                status_str = "空闲";
                break;

            case THREAD_WORKING:
                status_str = "工作中";
                break;

            case THREAD_ERROR:
                status_str = "错误";
                break;

            case THREAD_STOPPING:
                status_str = "停止中";
                break;

            default:
                status_str = "未知";
                break;
        }

        time_t uptime = now - stat.start_time;
        time_t idle_time = now - stat.last_activity_time;
        log_message(LOG_INFO, "  线程 " + stat.thread_id + ":");
        log_message(LOG_INFO, "    状态: " + status_str);

        if(!stat.current_task.empty())
        {
            log_message(LOG_INFO, "    当前任务: " + stat.current_task);
        }

        log_message(LOG_INFO, "    运行时间: " + std::to_string(uptime) + "秒");
        log_message(LOG_INFO, "    空闲时间: " + std::to_string(idle_time) + "秒");
        log_message(LOG_INFO, "    处理任务: " + std::to_string(stat.tasks_processed) +
                    " (完成: " + std::to_string(stat.tasks_completed) +
                    ", 失败: " + std::to_string(stat.tasks_failed) + ")");
        log_message(LOG_INFO, "    处理数据: " + std::to_string(stat.bytes_processed) + "字节, " +
                    std::to_string(stat.messages_processed) + "消息");
        log_message(LOG_INFO, "    错误数: " + std::to_string(stat.errors_count));

        if(!stat.last_error.empty())
        {
            log_message(LOG_INFO, "    最后错误: " + stat.last_error);
        }
    }

    log_message(LOG_INFO, "================================");
}

// 状态报告线程函数
void TaskManager::statusReporterFunc()
{
    const int REPORT_INTERVAL = 300; // 5分钟报告一次
    const int CLEANUP_INTERVAL = 60;  // 1分钟清理一次
    const int MAINTENANCE_INTERVAL = 3600; // 1小时执行一次维护
    time_t last_cleanup_time = time(nullptr);
    time_t last_maintenance_time = time(nullptr);

    while(running)
    {
        std::this_thread::sleep_for(std::chrono::seconds(30)); // 每30秒检查一次

        if(!running)
        {
            break;
        }

        time_t now = time(nullptr);

        // 更频繁地清理线程状态
        if(now - last_cleanup_time >= CLEANUP_INTERVAL)
        {
            forceResetZombieThreads(); // 先强制重置僵尸线程
            cleanupExpiredThreads();   // 再清理过期线程
            last_cleanup_time = now;
        }

        // 定期执行维护任务
        if(now - last_maintenance_time >= MAINTENANCE_INTERVAL)
        {
            performPeriodicMaintenance();
            last_maintenance_time = now;
        }

        // 定期打印完整报告
        if(now - last_status_report_time >= REPORT_INTERVAL)
        {
            printThreadStatusReport();
            last_status_report_time = now;
        }
    }
}

// 检查是否所有线程都空闲
bool TaskManager::areAllThreadsIdle() const
{
    // 首先检查活动任务数量
    int active_count = getActiveTaskCount();

    if(active_count > 0)
    {
        return false; // 如果有活动任务，肯定不是全部空闲
    }

    std::lock_guard<std::mutex> lock(thread_stats_mutex);

    // 如果没有活动线程，认为是空闲状态
    if(thread_stats.empty())
    {
        return true;
    }

    time_t now = time(nullptr);
    const int ZOMBIE_THREAD_THRESHOLD = 900; // 15分钟没有活动的工作线程被认为是僵尸线程

    // 检查所有线程是否都是空闲状态
    for(const auto &pair : thread_stats)
    {
        const ThreadStats &stats = pair.second;

        // 如果线程状态是空闲，继续检查下一个
        if(stats.status == THREAD_IDLE)
        {
            continue;
        }

        // 如果线程状态是工作中，但长时间没有活动，认为是僵尸线程
        if(stats.status == THREAD_WORKING)
        {
            time_t idle_time = now - stats.last_activity_time;

            // 如果线程长时间没有活动，认为是僵尸线程，视为空闲
            if(idle_time > ZOMBIE_THREAD_THRESHOLD)
            {
                log_message(LOG_DEBUG, "检测到僵尸线程: " + stats.thread_id +
                            ", 空闲时间: " + std::to_string(idle_time) + "秒，视为空闲");
                continue;
            }

            // 否则认为线程仍在工作
            log_message(LOG_DEBUG, "线程仍在工作: " + stats.thread_id +
                        ", 任务: " + (stats.current_task.empty() ? "无" : stats.current_task) +
                        ", 空闲时间: " + std::to_string(idle_time) + "秒");
            return false;
        }

        // 其他状态（错误、停止中等）也认为不是空闲
        return false;
    }

    return true;
}

// 从数据库获取失败任务
std::vector<std::pair<std::string, std::string>> TaskManager::getFailedTasksFromDB()
{
    std::vector<std::pair<std::string, std::string>> failed_tasks;

    if(!app_config.use_mysql)
    {
        return failed_tasks; // MySQL未启用，返回空列表
    }

    auto &pool = MySQLConnectionPool::getInstance();

    if(!pool.isInitialized())
    {
        log_message(LOG_WARN, "MySQL连接池未初始化，无法获取失败任务");
        return failed_tasks;
    }

    // 查询process_status不为1的记录，按接收时间排序
    std::string sql = "SELECT chip_id, datetime FROM device_info "
                      "WHERE process_status != 1 "
                      "ORDER BY receive_time ASC "
                      "LIMIT 50"; // 限制一次最多处理50个失败任务
    std::atomic<bool> query_completed(false);
    pool.queryAsync(sql, [&failed_tasks, &query_completed]
                    (MYSQL_RES * result, const std::string & error)
    {
        if(!result)
        {
            log_message(LOG_ERROR, "查询失败任务时出错: " + error);
        }
        else
        {
            // 解析查询结果
            MYSQL_ROW row;

            while((row = mysql_fetch_row(result)) != nullptr)
            {
                if(row[0] && row[1]) // chip_id 和 datetime
                {
                    failed_tasks.emplace_back(std::string(row[0]), std::string(row[1]));
                }
            }

            mysql_free_result(result);
            log_message(LOG_DEBUG, "成功查询到 " + std::to_string(failed_tasks.size()) + " 个失败任务");
        }

        query_completed = true;
    });

    // 等待查询完成，最多等待10秒
    for(int i = 0; i < 100; i++)
    {
        if(query_completed)
        {
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    if(!query_completed)
    {
        log_message(LOG_WARN, "查询失败任务超时");
    }

    return failed_tasks;
}

// 判断是否应该重试任务
bool TaskManager::shouldRetryTask(const std::string &chip_id, const std::string &datetime)
{
    std::lock_guard<std::mutex> lock(retry_mutex);
    std::string task_key = generateTaskKey(chip_id, datetime);
    time_t now = time(nullptr);
    // 检查重试次数限制
    const int MAX_RETRY_COUNT = 3;

    if(task_retry_count[task_key] >= MAX_RETRY_COUNT)
    {
        log_message(LOG_DEBUG, "任务 " + task_key + " 已达到最大重试次数(" +
                    std::to_string(MAX_RETRY_COUNT) + ")，跳过重试");
        return false;
    }

    // 检查重试冷却时间（避免频繁重试）
    const int RETRY_COOLDOWN = 1800; // 30分钟冷却时间

    if(task_last_retry_time.find(task_key) != task_last_retry_time.end())
    {
        time_t last_retry = task_last_retry_time[task_key];

        if(now - last_retry < RETRY_COOLDOWN)
        {
            log_message(LOG_DEBUG, "任务 " + task_key + " 仍在冷却期内，跳过重试");
            return false;
        }
    }

    // 检查任务是否已经在活动任务列表中
    {
        std::lock_guard<std::mutex> tasks_lock(tasks_mutex);

        if(active_tasks.find(task_key) != active_tasks.end())
        {
            log_message(LOG_DEBUG, "任务 " + task_key + " 已在活动任务列表中，跳过重试");
            return false;
        }
    }
    return true;
}

// 重处理失败任务线程函数
void TaskManager::retryFailedTasksFunc()
{
    const int CHECK_INTERVAL = 600; // 10分钟检查一次
    const int IDLE_WAIT_TIME = 300; // 所有线程空闲后等待5分钟再开始重处理

    while(running)
    {
        std::this_thread::sleep_for(std::chrono::seconds(60)); // 每分钟检查一次

        if(!running)
        {
            break;
        }

        time_t now = time(nullptr);

        // 检查是否到了检查时间
        if(now - last_retry_check_time < CHECK_INTERVAL)
        {
            continue;
        }

        // 检查是否所有线程都空闲
        if(!areAllThreadsIdle())
        {
            log_message(LOG_DEBUG, "有线程正在工作，暂不进行失败任务重处理");
            continue;
        }

        // 检查活动任务数量
        int active_count = getActiveTaskCount();

        if(active_count > 0)
        {
            log_message(LOG_DEBUG, "仍有 " + std::to_string(active_count) +
                        " 个活动任务，暂不进行失败任务重处理");
            continue;
        }

        log_message(LOG_INFO, "所有线程空闲，开始检查失败任务进行重处理...");
        // 等待一段时间确保系统稳定
        std::this_thread::sleep_for(std::chrono::seconds(IDLE_WAIT_TIME));

        if(!running)
        {
            break;
        }

        // 再次检查是否仍然空闲
        if(!areAllThreadsIdle() || getActiveTaskCount() > 0)
        {
            log_message(LOG_INFO, "等待期间有新任务到达，取消失败任务重处理");
            continue;
        }

        // 获取失败任务列表
        auto failed_tasks = getFailedTasksFromDB();

        if(failed_tasks.empty())
        {
            log_message(LOG_INFO, "没有找到需要重处理的失败任务");
            last_retry_check_time = now;
            continue;
        }

        log_message(LOG_INFO, "找到 " + std::to_string(failed_tasks.size()) + " 个失败任务，开始重处理");
        int retry_count = 0;

        for(const auto &task : failed_tasks)
        {
            if(!running)
            {
                break;
            }

            const std::string &chip_id = task.first;
            const std::string &datetime = task.second;

            // 检查是否应该重试这个任务
            if(!shouldRetryTask(chip_id, datetime))
            {
                continue;
            }

            // 创建设备信息对象进行重处理
            binary_stream::DeviceInfo device_info;
            device_info.set_chip_id(chip_id);
            device_info.set_datetime(datetime);
            device_info.set_log_file_name("retry_" + chip_id + "_" + datetime + ".log");
            device_info.set_log_file_size(0); // 重试时大小未知

            // 添加到任务队列
            if(addTask(device_info))
            {
                // 更新重试记录
                std::lock_guard<std::mutex> lock(retry_mutex);
                std::string task_key = generateTaskKey(chip_id, datetime);
                task_retry_count[task_key]++;
                task_last_retry_time[task_key] = now;
                retry_count++;
                log_message(LOG_INFO, "已将失败任务重新加入队列: " + task_key +
                            " (重试次数: " + std::to_string(task_retry_count[task_key]) + ")");

                // 限制同时重试的任务数量
                if(retry_count >= 10)
                {
                    log_message(LOG_INFO, "已重试 " + std::to_string(retry_count) + " 个任务，暂停重试");
                    break;
                }

                // 在任务之间稍作延迟
                std::this_thread::sleep_for(std::chrono::seconds(2));
            }
        }

        if(retry_count > 0)
        {
            log_message(LOG_INFO, "失败任务重处理完成，共重试了 " + std::to_string(retry_count) + " 个任务");
        }

        last_retry_check_time = now;
    }
}

// 清理过期的线程状态
void TaskManager::cleanupExpiredThreads()
{
    std::lock_guard<std::mutex> lock(thread_stats_mutex);
    time_t now = time(nullptr);
    const int THREAD_EXPIRE_TIME = 300; // 5分钟没有活动的线程被认为是过期的
    auto it = thread_stats.begin();
    int cleaned_count = 0;

    while(it != thread_stats.end())
    {
        const ThreadStats &stats = it->second;
        // 检查线程是否过期
        bool should_cleanup = false;

        // 如果线程空闲时间超过5分钟，则清理
        if(stats.status == THREAD_IDLE &&
                (now - stats.last_activity_time) > THREAD_EXPIRE_TIME)
        {
            should_cleanup = true;
        }

        // 如果线程显示工作中，但空闲时间超过10分钟，且当前任务为空，则是僵尸线程
        if(stats.status == THREAD_WORKING &&
                (now - stats.last_activity_time) > 600 &&
                stats.current_task.empty())
        {
            should_cleanup = true;
            log_message(LOG_WARN, "清理僵尸线程: " + stats.thread_id +
                        ", 空闲时间: " + std::to_string(now - stats.last_activity_time) + "秒");
        }

        // 如果线程显示工作中，但空闲时间超过20分钟，无论是否有当前任务，都清理
        // 数据接收任务可能需要长时间等待，20分钟是更安全的阈值
        if(stats.status == THREAD_WORKING &&
                (now - stats.last_activity_time) > 1200)
        {
            should_cleanup = true;
            log_message(LOG_WARN, "清理长时间无活动的工作线程: " + stats.thread_id +
                        ", 空闲时间: " + std::to_string(now - stats.last_activity_time) + "秒" +
                        ", 当前任务: " + (stats.current_task.empty() ? "无" : stats.current_task));
        }

        if(should_cleanup)
        {
            log_message(LOG_DEBUG, "清理过期线程状态: " + stats.thread_id);
            it = thread_stats.erase(it);
            cleaned_count++;
        }
        else
        {
            ++it;
        }
    }

    if(cleaned_count > 0)
    {
        log_message(LOG_INFO, "已清理 " + std::to_string(cleaned_count) + " 个过期线程状态");
    }
}

// 强制重置僵尸线程状态
void TaskManager::forceResetZombieThreads()
{
    std::lock_guard<std::mutex> lock(thread_stats_mutex);
    time_t now = time(nullptr);
    int reset_count = 0;

    for(auto &pair : thread_stats)
    {
        ThreadStats &stats = pair.second;
        // 根据任务类型设置不同的超时阈值
        int timeout_threshold = 900; // 默认15分钟

        // 如果是数据接收任务，给予更长的等待时间
        if(!stats.current_task.empty() &&
                (stats.current_task.find("等待消息") != std::string::npos ||
                 stats.current_task.find("接收数据") != std::string::npos))
        {
            timeout_threshold = 1800; // 30分钟
        }

        // 如果线程显示工作中，但空闲时间超过阈值，强制设置为空闲
        if(stats.status == THREAD_WORKING &&
                (now - stats.last_activity_time) > timeout_threshold)
        {
            log_message(LOG_WARN, "强制重置僵尸线程状态: " + stats.thread_id +
                        ", 空闲时间: " + std::to_string(now - stats.last_activity_time) + "秒" +
                        ", 当前任务: " + (stats.current_task.empty() ? "无" : stats.current_task));
            stats.status = THREAD_IDLE;
            stats.current_task = "";
            stats.last_activity_time = now;
            reset_count++;
        }
    }

    if(reset_count > 0)
    {
        log_message(LOG_INFO, "已强制重置 " + std::to_string(reset_count) + " 个僵尸线程状态");
    }
}

// 清理旧的已完成任务记录
void TaskManager::cleanupOldCompletedTasks()
{
    std::lock_guard<std::mutex> lock(state_mutex);
    time_t now = time(nullptr);
    const int TASK_EXPIRE_TIME = 86400; // 24小时后清理已完成任务记录
    auto it = completed_tasks.begin();
    int cleaned_count = 0;

    while(it != completed_tasks.end())
    {
        const FileProcessingState &state = it->second;

        // 清理24小时前完成的任务记录
        if((now - state.last_update) > TASK_EXPIRE_TIME &&
                (state.status == TASK_COMPLETED || state.status == TASK_FAILED))
        {
            it = completed_tasks.erase(it);
            cleaned_count++;
        }
        else
        {
            ++it;
        }
    }

    if(cleaned_count > 0)
    {
        log_message(LOG_INFO, "已清理 " + std::to_string(cleaned_count) + " 个过期的任务记录");
    }
}

// 执行定期维护任务
void TaskManager::performPeriodicMaintenance()
{
    log_message(LOG_DEBUG, "开始执行定期维护任务");
    // 清理过期的线程状态
    cleanupExpiredThreads();
    // 清理旧的已完成任务记录
    cleanupOldCompletedTasks();
    // 清理重试记录中的过期条目
    {
        std::lock_guard<std::mutex> lock(retry_mutex);
        time_t now = time(nullptr);
        const int RETRY_RECORD_EXPIRE_TIME = 604800; // 7天后清理重试记录
        auto retry_it = task_retry_count.begin();
        int retry_cleaned = 0;

        while(retry_it != task_retry_count.end())
        {
            const std::string &task_key = retry_it->first;
            auto time_it = task_last_retry_time.find(task_key);

            if(time_it != task_last_retry_time.end() &&
                    (now - time_it->second) > RETRY_RECORD_EXPIRE_TIME)
            {
                task_last_retry_time.erase(time_it);
                retry_it = task_retry_count.erase(retry_it);
                retry_cleaned++;
            }
            else
            {
                ++retry_it;
            }
        }

        if(retry_cleaned > 0)
        {
            log_message(LOG_INFO, "已清理 " + std::to_string(retry_cleaned) + " 个过期的重试记录");
        }
    }
    log_message(LOG_DEBUG, "定期维护任务完成");
}

// 健康检查
bool TaskManager::isHealthy() const
{
    // 检查是否正在运行
    if(!running)
    {
        return false;
    }

    // 检查关键线程是否存在
    if(!task_dispatcher_thread.joinable() ||
            !status_reporter_thread.joinable() ||
            !retry_failed_tasks_thread.joinable())
    {
        return false;
    }

    // 检查是否有过多的错误线程
    std::lock_guard<std::mutex> lock(thread_stats_mutex);
    int error_threads = 0;
    int total_threads = thread_stats.size();

    for(const auto &pair : thread_stats)
    {
        if(pair.second.status == THREAD_ERROR)
        {
            error_threads++;
        }
    }

    // 如果超过50%的线程处于错误状态，认为不健康
    if(total_threads > 0 && (error_threads * 2) > total_threads)
    {
        return false;
    }

    return true;
}