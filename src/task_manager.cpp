#include "exporter.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <sys/stat.h>  // 添加stat头文件
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <unordered_set>
#include "nlohmann_json.hpp" // 添加JSON库

// 声明外部函数
extern bool update_device_info_status(const std::string &chip_id, const std::string &datetime,
                                      int status, uint64_t processed_size,
                                      const std::string &actual_checksum, const std::string &error_message);

// 声明外部变量
extern AppConfig app_config; // 在exporter.cpp中定义

// 使用nlohmann/json库
using json = nlohmann::json;

// 计算简单的校验和（与producer.cpp中的计算方法相同）
uint32_t calculate_checksum(const uint8_t *data, size_t len)
{
    uint32_t checksum = 0;

    for(size_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }

    return checksum;
}

// MD5辅助函数
// 初始化MD5上下文
void init_md5_context(MD5_CTX *context)
{
    MD5_Init(context);
}

// 更新MD5上下文
void update_md5_context(MD5_CTX *context, const unsigned char *data, size_t length)
{
    MD5_Update(context, data, length);
}

// 完成MD5计算并返回十六进制字符串
std::string finalize_md5_context(MD5_CTX *context)
{
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5_CTX context_copy = *context; // 复制上下文，避免修改原始上下文
    MD5_Final(digest, &context_copy);
    std::stringstream ss;

    for(int i = 0; i < MD5_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)digest[i];
    }

    return ss.str();
}

// 计算消息指纹 - 使用消息内容和序列号的组合
std::string calculate_message_fingerprint(const std::string &payload, uint64_t sequence_number,
        const std::string &chip_id, const std::string &datetime)
{
    // 创建SHA256上下文
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    // 更新上下文 - 添加序列号
    std::string seq_str = std::to_string(sequence_number);
    SHA256_Update(&sha256, seq_str.c_str(), seq_str.length());
    // 更新上下文 - 添加芯片ID
    SHA256_Update(&sha256, chip_id.c_str(), chip_id.length());
    // 更新上下文 - 添加日期时间
    SHA256_Update(&sha256, datetime.c_str(), datetime.length());
    // 更新上下文 - 添加负载数据
    SHA256_Update(&sha256, payload.c_str(), payload.length());
    // 完成计算
    SHA256_Final(hash, &sha256);
    // 转换为十六进制字符串
    std::stringstream ss;

    for(int i = 0; i < SHA256_DIGEST_LENGTH; i++)
    {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
    }

    return ss.str();
}

// 按序列号排序所有消息，并计算MD5
std::string calculate_md5_from_messages(const std::map<uint64_t, MessageData> &sorted_messages)
{
    MD5_CTX context;
    init_md5_context(&context);
    // 记录消息数量和总大小，用于调试
    size_t message_count = sorted_messages.size();
    size_t total_size = 0;
    // 检查序列号是否连续
    uint64_t expected_seq = 0;
    bool first_message = true;
    bool sequence_gap = false;

    // 遍历排序后的消息集合，按序列号顺序计算MD5
    for(const auto &pair : sorted_messages)
    {
        uint64_t seq = pair.first;
        const MessageData &data = pair.second;

        // 检查序列号是否连续
        if(first_message)
        {
            expected_seq = seq;
            first_message = false;
        }
        else if(seq != expected_seq)
        {
            log_message(LOG_WARN, "MD5计算中发现序列号不连续: 期望 " + std::to_string(expected_seq) +
                        ", 实际 " + std::to_string(seq));
            sequence_gap = true;
        }

        expected_seq = seq + 1;
        // 更新MD5上下文
        update_md5_context(&context,
                           reinterpret_cast<const unsigned char *>(data.payload.data()),
                           data.payload.size());
        total_size += data.payload.size();
    }

    // 记录MD5计算的统计信息
    log_message(LOG_INFO, "MD5计算统计: 消息数量=" + std::to_string(message_count) +
                ", 总大小=" + std::to_string(total_size) + " 字节" +
                (sequence_gap ? ", 存在序列号不连续" : ", 序列号连续"));
    return finalize_md5_context(&context);
}

// 使用nlohmann/json库替换SimpleJson
namespace JsonUtils
{
    // 将任务状态保存为JSON格式字符串
    std::string serializeState(const std::map<std::string, FileProcessingState> &states)
    {
        json tasks_array = json::array();

        for(const auto &pair : states)
        {
            const FileProcessingState &state = pair.second;
            json task_obj =
            {
                {"chip_id", state.chip_id},
                {"datetime", state.datetime},
                {"filename", state.filename},
                {"expected_size", state.expected_size},
                {"processed_size", state.processed_size},
                {"min_sequence", state.min_sequence},
                {"max_sequence", state.max_sequence},
                {"expected_messages", state.expected_messages},
                {"actual_messages", state.actual_messages},
                {"checksum", state.checksum},
                {"start_time", state.start_time},
                {"last_update", state.last_update},
                {"status", static_cast<int>(state.status)},
                {"retry_count", state.retry_count},
                {"error_message", state.error_message}
            };
            tasks_array.push_back(task_obj);
        }

        json root = {{"tasks", tasks_array}};
        return root.dump(2); // 使用2个空格缩进
    }

    // 从JSON字符串解析任务状态
    bool parseState(const std::string &json_str, std::map<std::string, FileProcessingState> &states)
    {
        try
        {
            json root = json::parse(json_str);

            if(!root.contains("tasks") || !root["tasks"].is_array())
            {
                log_message(LOG_ERROR, "无效的JSON格式: 缺少tasks数组");
                return false;
            }

            states.clear();

            for(const auto &task_obj : root["tasks"])
            {
                FileProcessingState state;
                state.chip_id = task_obj["chip_id"];
                state.datetime = task_obj["datetime"];
                state.filename = task_obj["filename"];
                state.expected_size = task_obj["expected_size"];
                state.processed_size = task_obj["processed_size"];
                state.min_sequence = task_obj["min_sequence"];
                state.max_sequence = task_obj["max_sequence"];
                state.expected_messages = task_obj["expected_messages"];
                state.actual_messages = task_obj["actual_messages"];
                state.checksum = task_obj["checksum"];
                state.start_time = task_obj["start_time"];
                state.last_update = task_obj["last_update"];
                state.status = static_cast<TaskStatus>(task_obj["status"].get<int>());
                state.retry_count = task_obj["retry_count"];
                state.error_message = task_obj["error_message"];
                std::string task_key = state.chip_id + "_" + state.datetime;
                states[task_key] = state;
            }

            return true;
        }
        catch(const std::exception &e)
        {
            log_message(LOG_ERROR, std::string("解析状态文件失败: ") + e.what());
            return false;
        }
    }
}

// 任务管理器实现
TaskManager::TaskManager(const AppConfig &config)
    : config(config), running(true)
{
}

TaskManager::~TaskManager()
{
    stopAllTasks();
}

// 生成任务键
std::string TaskManager::generateTaskKey(const std::string &chip_id, const std::string &datetime)
{
    return chip_id + "_" + datetime;
}

// 初始化任务管理器
bool TaskManager::initialize()
{
    // 启动任务调度线程
    task_dispatcher_thread = std::thread(&TaskManager::taskDispatcherFunc, this);
    return true;
}

// 添加任务
bool TaskManager::addTask(const binary_stream::DeviceInfo &info)
{
    std::string task_key = generateTaskKey(info.chip_id(), info.datetime());
    // 检查任务是否已存在
    {
        std::lock_guard<std::mutex> lock(tasks_mutex);

        if(active_tasks.find(task_key) != active_tasks.end())
        {
            // 任务已存在且活动中
            log_message(LOG_INFO, "任务已存在: " + task_key);
            return false;
        }
    }
    {
        std::lock_guard<std::mutex> lock(state_mutex);
        auto it = completed_tasks.find(task_key);

        if(it != completed_tasks.end() && it->second.status == TASK_COMPLETED)
        {
            // 任务已完成，但要求重新获取
            log_message(LOG_INFO, "任务 " + task_key + " 已完成，将重新执行");
            completed_tasks.erase(it);
        }
    }
    // 创建新任务
    auto task = std::make_shared<DataRetrievalTask>();
    task->chip_id = info.chip_id();
    task->datetime = info.datetime();
    task->filename = info.log_file_name();
    task->file_size = info.log_file_size();
    task->checksum = info.file_checksum();
    task->topic = get_topic_for_chip_id(info.chip_id());
    task->active = false;
    // 设置输出路径
    task->output_path = config.output_dir + "/" + info.chip_id() + "/" + info.log_file_name();
    // 初始化状态
    task->state.chip_id = info.chip_id();
    task->state.datetime = info.datetime();
    task->state.filename = info.log_file_name();
    task->state.expected_size = info.log_file_size();
    task->state.checksum = task->checksum;
    task->state.status = TASK_PENDING;
    task->state.start_time = time(nullptr);
    task->state.last_update = time(nullptr);
    // 添加到队列
    {
        std::lock_guard<std::mutex> lock(queue_mutex);
        task_queue.push(task);
    }
    // 通知调度器
    queue_cv.notify_one();
    log_message(LOG_INFO, "已添加任务: " + task_key + ", 文件大小: " + std::to_string(
                                task->file_size) + " 字节");
    return true;
}

// 获取任务状态
FileProcessingState TaskManager::getTaskState(const std::string &chip_id, const std::string &datetime)
{
    std::string task_key = generateTaskKey(chip_id, datetime);
    // 先检查活动任务
    {
        std::lock_guard<std::mutex> lock(tasks_mutex);
        auto it = active_tasks.find(task_key);

        if(it != active_tasks.end())
        {
            return it->second->state;
        }
    }
    // 再检查已完成任务
    {
        std::lock_guard<std::mutex> lock(state_mutex);
        auto it = completed_tasks.find(task_key);

        if(it != completed_tasks.end())
        {
            return it->second;
        }
    }
    // 未找到任务
    FileProcessingState empty_state;
    empty_state.chip_id = chip_id;
    empty_state.datetime = datetime;
    empty_state.status = TASK_PENDING;
    return empty_state;
}

// 获取活动任务数
int TaskManager::getActiveTaskCount() const
{
    std::lock_guard<std::mutex> lock(tasks_mutex);
    return active_tasks.size();
}

// 保存状态
bool TaskManager::saveState() const
{
    // 使用简单JSON保存状态
    std::map<std::string, FileProcessingState> all_states;
    // 添加活动任务
    {
        std::lock_guard<std::mutex> lock(tasks_mutex);

        for(const auto &pair : active_tasks)
        {
            all_states[pair.first] = pair.second->state;
        }
    }
    // 添加已完成任务
    {
        std::lock_guard<std::mutex> lock(state_mutex);

        for(const auto &pair : completed_tasks)
        {
            all_states[pair.first] = pair.second;
        }
    }

    // 写入文件
    try
    {
        std::ofstream file(config.state_file);

        if(!file.is_open())
        {
            log_message(LOG_ERROR, "无法打开状态文件: " + config.state_file);
            return false;
        }

        file << JsonUtils::serializeState(all_states);
        file.close();
        return true;
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, "保存状态文件失败: " + std::string(e.what()));
        return false;
    }
}

// 加载状态
bool TaskManager::loadState()
{
    try
    {
        std::ifstream file(config.state_file);

        if(!file.is_open())
        {
            log_message(LOG_WARN, "无法打开状态文件: " + config.state_file);
            return false;
        }

        std::stringstream buffer;
        buffer << file.rdbuf();
        file.close();
        // 解析JSON
        std::map<std::string, FileProcessingState> loaded_states;

        if(!JsonUtils::parseState(buffer.str(), loaded_states))
        {
            log_message(LOG_ERROR, "解析状态文件失败");
            return false;
        }

        // 处理加载的任务
        {
            std::lock_guard<std::mutex> lock(state_mutex);

            for(const auto &pair : loaded_states)
            {
                std::string task_key = pair.first;
                FileProcessingState state = pair.second;

                if(state.status == TASK_COMPLETED)
                {
                    // 完成的任务直接添加到已完成列表
                    completed_tasks[task_key] = state;
                }
                else
                {
                    // 未完成的任务转换为新任务
                    auto task = std::make_shared<DataRetrievalTask>();
                    task->chip_id = state.chip_id;
                    task->datetime = state.datetime;
                    task->filename = state.filename;
                    task->file_size = state.expected_size;
                    task->checksum = state.checksum;
                    task->topic = get_topic_for_chip_id(task->chip_id);
                    task->active = false;
                    // 设置输出路径
                    task->output_path = config.output_dir + "/" + task->chip_id + "/" + task->filename;
                    // 初始化状态
                    task->state = state;
                    task->state.status = TASK_PENDING; // 重置为等待状态
                    // 添加到队列
                    std::lock_guard<std::mutex> queue_lock(queue_mutex);
                    task_queue.push(task);
                }
            }
        }
        return true;
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, "加载状态文件失败: " + std::string(e.what()));
        return false;
    }
}

// 停止所有任务
void TaskManager::stopAllTasks()
{
    running = false;
    // 首先停止所有活动任务
    {
        std::lock_guard<std::mutex> lock(tasks_mutex);

        for(auto &pair : active_tasks)
        {
            auto &task = pair.second;
            task->active = false;

            // 安全关闭消费者
            if(task->consumer)
            {
                try
                {
                    task->consumer->close();
                }
                catch(const std::exception &e)
                {
                    log_message(LOG_ERROR, "关闭消费者失败: " + std::string(e.what()));
                }
            }
        }
    }

    // 等待所有任务线程完成
    if(task_dispatcher_thread.joinable())
    {
        task_dispatcher_thread.join();
    }

    active_tasks.clear();
}

// 更新任务状态
void TaskManager::updateTaskState(const std::string &task_key, const FileProcessingState &state)
{
    std::lock_guard<std::mutex> lock(state_mutex);
    completed_tasks[task_key] = state;
}

// 任务调度器线程函数
void TaskManager::taskDispatcherFunc()
{
    while(running)
    {
        std::shared_ptr<DataRetrievalTask> next_task;
        // 获取下一个任务
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            queue_cv.wait(lock, [this] { return !running || !task_queue.empty(); });

            if(!running)
            {
                break;
            }

            // 检查是否达到最大并发任务数
            if(getActiveTaskCount() >= config.max_tasks)
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }

            if(!task_queue.empty())
            {
                next_task = task_queue.front();
                task_queue.pop();
            }
        }

        // 处理下一个任务
        if(next_task)
        {
            std::string task_key = generateTaskKey(next_task->chip_id, next_task->datetime);
            // 首先检查任务是否已经在活动列表中
            {
                std::lock_guard<std::mutex> lock(tasks_mutex);

                if(active_tasks.find(task_key) != active_tasks.end())
                {
                    log_message(LOG_WARN, "任务已在活动列表中，跳过: " + task_key);
                    continue;
                }

                // 添加到活动任务
                active_tasks[task_key] = next_task;
            }

            // 确保没有遗留的消费者
            if(next_task->consumer)
            {
                try
                {
                    next_task->consumer->close();
                    next_task->consumer.reset();
                }
                catch(...)
                {
                    // 忽略异常，确保创建新的消费者
                }
            }

            // 创建消费者前记录详细信息
            log_message(LOG_INFO, "正在为任务创建消费者: " + task_key + ", 主题: " + next_task->topic);
            // 创建消费者，传入主题和芯片ID
            next_task->consumer.reset(createConsumer(next_task->topic, next_task->chip_id));

            if(!next_task->consumer)
            {
                std::string error_message = "无法创建消费者";
                log_message(LOG_ERROR, error_message + ": " + task_key);
                // 更新任务状态
                next_task->state.status = TASK_FAILED;
                next_task->state.error_message = error_message;
                updateTaskState(task_key, next_task->state);

                // 更新MySQL数据库中的设备信息状态
                if(app_config.use_mysql)
                {
                    update_device_info_status(
                                    next_task->chip_id,
                                    next_task->datetime,
                                    3, // 3表示处理失败
                                    0, // 处理大小为0
                                    "", // 没有校验和
                                    error_message
                    );
                }

                // 从活动任务中移除
                std::lock_guard<std::mutex> lock(tasks_mutex);
                active_tasks.erase(task_key);
                continue;
            }

            // 确保输出目录存在
            if(!ensureOutputDir(next_task->chip_id))
            {
                std::string error_message = "无法创建输出目录";
                log_message(LOG_ERROR, error_message + ": " + next_task->chip_id);
                // 更新任务状态
                next_task->state.status = TASK_FAILED;
                next_task->state.error_message = error_message;
                updateTaskState(task_key, next_task->state);

                // 更新MySQL数据库中的设备信息状态
                if(app_config.use_mysql)
                {
                    update_device_info_status(
                                    next_task->chip_id,
                                    next_task->datetime,
                                    3, // 3表示处理失败
                                    0, // 处理大小为0
                                    "", // 没有校验和
                                    error_message
                    );
                }

                // 从活动任务中移除
                std::lock_guard<std::mutex> lock(tasks_mutex);
                active_tasks.erase(task_key);
                continue;
            }

            // 检查目录访问权限
            if(access((config.output_dir + "/" + next_task->chip_id).c_str(), W_OK) != 0)
            {
                log_message(LOG_ERROR, "输出目录没有写入权限: " + config.output_dir + "/" + next_task->chip_id);
                log_message(LOG_INFO, "尝试修复权限问题...");
                // 尝试修复权限
                chmod((config.output_dir + "/" + next_task->chip_id).c_str(), 0755);
            }

            // 启动处理线程
            next_task->active = true;
            next_task->thread = std::thread(&TaskManager::taskProcessingFunc, this, next_task);
            // 将线程设为分离状态，这样它可以自行清理资源
            next_task->thread.detach();
            log_message(LOG_INFO, "已启动任务: " + task_key);
        }
    }
}

// 确保输出目录存在
bool TaskManager::ensureOutputDir(const std::string &chip_id)
{
    std::string dir_path = config.output_dir;

    if(!chip_id.empty())
    {
        dir_path += "/" + chip_id;
    }

    // 创建目录
    if(access(dir_path.c_str(), F_OK) != 0)
    {
        if(mkdir(dir_path.c_str(), 0755) != 0 && errno != EEXIST)
        {
            log_message(LOG_ERROR, "无法创建目录: " + dir_path + ", 错误: " + strerror(errno));
            return false;
        }
    }

    return true;
}

// 创建Kafka消费者
RdKafka::KafkaConsumer *TaskManager::createConsumer(const std::string &topic, const std::string &chip_id)
{
    log_message(LOG_INFO, "开始创建Kafka消费者，主题: " + topic);
    std::string errstr;
    RdKafka::Conf *conf = RdKafka::Conf::create(RdKafka::Conf::CONF_GLOBAL);
    // 设置Kafka配置
    log_message(LOG_DEBUG, "设置Kafka配置: bootstrap.servers=" + config.kafka_brokers);

    if(conf->set("bootstrap.servers", config.kafka_brokers, errstr) != RdKafka::Conf::CONF_OK)
    {
        log_message(LOG_ERROR, "设置bootstrap.servers失败: " + errstr);
    }

    // 为每个消费者生成唯一的消费者组ID，避免组成员冲突
    std::string unique_group_id = std::string(CONSUMER_GROUP_ID) + "_" + chip_id + "_" +
                                  std::to_string(std::chrono::system_clock::now().time_since_epoch().count());
    log_message(LOG_DEBUG, "设置Kafka配置: group.id=" + unique_group_id);

    if(conf->set("group.id", unique_group_id.c_str(), errstr) != RdKafka::Conf::CONF_OK)
    {
        log_message(LOG_ERROR, "设置group.id失败: " + errstr);
    }

    // 设置更多的配置，并记录日志
    std::vector<std::pair<std::string, std::string>> configs =
    {
        {"auto.offset.reset", "earliest"},
        {"enable.auto.commit", "false"}, // 禁用自动提交，因为我们使用assign而不是subscribe
        {"session.timeout.ms", "30000"},
        {"max.poll.interval.ms", "600000"}, // 增加轮询间隔，防止消费者被踢出组
        {"heartbeat.interval.ms", "10000"}, // 增加心跳间隔
        {"fetch.wait.max.ms", "500"}, // 减少等待时间，更频繁地检查消息
        {"fetch.error.backoff.ms", "500"}, // 减少错误回退时间
        {"coordinator.query.interval.ms", "60000"} // 减少协调器查询频率
    };

    for(const auto &cfg : configs)
    {
        log_message(LOG_DEBUG, "设置Kafka配置: " + cfg.first + "=" + cfg.second);

        if(conf->set(cfg.first, cfg.second, errstr) != RdKafka::Conf::CONF_OK)
        {
            log_message(LOG_ERROR, "设置" + cfg.first + "失败: " + errstr);
        }
    }

    // 创建消费者
    log_message(LOG_INFO, "创建Kafka消费者实例...");
    RdKafka::KafkaConsumer *consumer = RdKafka::KafkaConsumer::create(conf, errstr);

    if(!consumer)
    {
        log_message(LOG_ERROR, "无法创建Kafka消费者: " + errstr);
        delete conf;
        return nullptr;
    }

    // 获取消费者元数据
    log_message(LOG_INFO, "获取Kafka集群元数据...");
    RdKafka::Metadata *metadata;
    RdKafka::ErrorCode meta_err = consumer->metadata(true, nullptr, &metadata, 5000);

    if(meta_err == RdKafka::ERR_NO_ERROR)
    {
        log_message(LOG_INFO, "Kafka集群: " + std::string(metadata->orig_broker_name()) +
                    " (ID: " + std::to_string(metadata->orig_broker_id()) + ")");
        // 检查主题是否存在
        bool topic_exists = false;

        for(const auto &topic_meta : *metadata->topics())
        {
            if(topic_meta->topic() == topic)
            {
                topic_exists = true;
                log_message(LOG_INFO, "主题 " + topic + " 存在，分区数: " +
                            std::to_string(topic_meta->partitions()->size()));
                break;
            }
        }

        if(!topic_exists)
        {
            log_message(LOG_WARN, "主题 " + topic + " 在集群中不存在，可能会导致消费失败");
        }

        delete metadata;
    }
    else
    {
        log_message(LOG_WARN, "获取元数据失败: " + std::string(RdKafka::err2str(meta_err)));
    }

    // 计算芯片ID对应的分区
    int32_t partition = get_partition_for_chip_id(chip_id);
    // 创建TopicPartition对象，只订阅特定分区
    std::vector<RdKafka::TopicPartition *> partitions;
    RdKafka::TopicPartition *tp = RdKafka::TopicPartition::create(topic, partition);
    partitions.push_back(tp);
    log_message(LOG_INFO, "手动分配分区: " + topic + "[" + std::to_string(partition) + "]");
    // 使用assign而不是subscribe，手动分配分区
    RdKafka::ErrorCode err = consumer->assign(partitions);

    // 释放TopicPartition对象
    for(auto &p : partitions)
    {
        delete p;
    }

    if(err != RdKafka::ERR_NO_ERROR)
    {
        log_message(LOG_ERROR, "无法分配分区 " + topic + "[" + std::to_string(partition) + "]: " + RdKafka::err2str(err));
        delete consumer;
        return nullptr;
    }

    // 获取分区分配
    std::vector<RdKafka::TopicPartition *> assigned_partitions;
    RdKafka::ErrorCode assign_err = consumer->assignment(assigned_partitions);

    if(assign_err == RdKafka::ERR_NO_ERROR)
    {
        std::stringstream ss;
        ss << "初始分区分配: ";

        for(const auto &tp : assigned_partitions)
        {
            ss << tp->topic() << "[" << tp->partition() << "] ";
            delete tp;
        }

        log_message(LOG_INFO, ss.str());
    }
    else
    {
        log_message(LOG_WARN, "获取分区分配失败: " + std::string(RdKafka::err2str(assign_err)));
    }

    log_message(LOG_INFO, "已成功创建并订阅主题: " + topic);
    return consumer;
}

// 任务处理线程函数
void TaskManager::taskProcessingFunc(std::shared_ptr<DataRetrievalTask> task)
{
    std::string task_key = generateTaskKey(task->chip_id, task->datetime);

    try
    {
        // 更新任务状态
        task->state.status = TASK_RUNNING;
        task->state.last_update = time(nullptr);

        // 初始化MD5上下文
        if(!task->md5_initialized)
        {
            init_md5_context(&task->md5_context);
            task->md5_initialized = true;
        }

        // 准备文件缓冲区和输出文件
        FileBuffer buffer;
        std::map<uint64_t, bool> received_sequences;
        // 增加一个有序的消息映射，按序列号排序所有接收到的消息
        std::map<uint64_t, MessageData> sorted_messages;
        // 打开输出文件
        std::ofstream output_file(task->output_path, std::ios::binary | std::ios::trunc);

        if(!output_file.is_open())
        {
            std::string error_message = "无法打开输出文件";
            log_message(LOG_ERROR, error_message + ": " + task->output_path);
            // 更新任务状态
            task->state.status = TASK_FAILED;
            task->state.error_message = error_message;
            updateTaskState(task_key, task->state);

            // 更新MySQL数据库中的设备信息状态
            if(app_config.use_mysql)
            {
                update_device_info_status(
                                task->chip_id,
                                task->datetime,
                                3, // 3表示处理失败
                                0, // 处理大小为0
                                "", // 没有校验和
                                error_message
                );
            }

            // 从活动任务中移除
            {
                std::lock_guard<std::mutex> lock(tasks_mutex);
                auto it = active_tasks.find(task_key);

                if(it != active_tasks.end())
                {
                    try
                    {
                        if(it->second && it->second->consumer)
                        {
                            it->second->consumer->close();
                            it->second->consumer.reset();
                        }
                    }
                    catch(...)
                    {
                        // 忽略异常
                    }

                    active_tasks.erase(it);
                }
            }
            task->active = false;
            return;
        }

        // 确保文件打开成功后立即写入
        output_file << std::flush;
        time_t start_time = time(nullptr);
        uint64_t message_count = 0;
        uint64_t total_bytes = 0;
        bool finished = false;

        while(running && task->active && !finished)
        {
            // 获取消息
            RdKafka::Message *msg = task->consumer->consume(POLL_TIMEOUT_MS);

            if(msg)
            {
                switch(msg->err())
                {
                    case RdKafka::ERR_NO_ERROR:
                        {
                            // 解析消息
                            binary_stream::StreamData stream_data;

                            if(stream_data.ParseFromArray(msg->payload(), static_cast<int>(msg->len())))
                            {
                                // 验证单个数据包的校验和
                                // 直接获取校验和，不需要检查是否存在
                                uint32_t received_checksum = stream_data.checksum();
                                uint32_t calculated_checksum = calculate_checksum(
                                        reinterpret_cast<const uint8_t *>(stream_data.payload().data()),
                                        stream_data.payload().size());

                                if(received_checksum != calculated_checksum)
                                {
                                    log_message(LOG_WARN, "数据包校验和不匹配: 序列号=" +
                                                std::to_string(stream_data.sequence_number()) +
                                                ", 期望=" + std::to_string(received_checksum) +
                                                ", 计算得到=" + std::to_string(calculated_checksum));
                                    // 严格校验数据完整性，跳过校验和不匹配的数据包
                                    log_message(LOG_ERROR, "跳过校验和不匹配的数据包，确保数据完整性");
                                    continue;
                                }

                                // 不再立即更新MD5，而是先排序再更新

                                // 验证芯片ID和日期时间
                                if(stream_data.chip_id() == task->chip_id && stream_data.datetime() == task->datetime)
                                {
                                    // 处理消息
                                    uint64_t seq_num = stream_data.sequence_number();
                                    std::string payload = stream_data.payload();
                                    // 增加任务的总处理消息计数
                                    task->total_processed_count++;

                                    // 清理过期的指纹记录 - 每10000条消息检查一次
                                    if(task->total_processed_count % 10000 == 0)
                                    {
                                        time_t now = time(nullptr);

                                        // 如果去重窗口超过1小时，清空指纹集合
                                        if(now - task->dedup_window_start > 3600)
                                        {
                                            log_message(LOG_INFO, "清理去重窗口: " + task_key +
                                                        ", 清除 " + std::to_string(task->message_fingerprints.size()) +
                                                        " 条指纹记录");
                                            task->message_fingerprints.clear();
                                            task->dedup_window_start = now;
                                        }
                                    }

                                    // 第一级去重：基于序列号
                                    bool is_duplicate = false;

                                    if(received_sequences.find(seq_num) != received_sequences.end())
                                    {
                                        task->seq_duplicate_count++;
                                        task->duplicate_count++;
                                        is_duplicate = true;

                                        // 只在特定情况下输出日志，减少日志数量
                                        if(task->duplicate_count == 1 ||
                                                seq_num != task->last_reported_duplicate_seq ||
                                                task->duplicate_count % 100 == 0)
                                        {
                                            log_message(LOG_WARN, "检测到序列号重复: " + task_key +
                                                        ", 序列号: " + std::to_string(seq_num) +
                                                        ", 累计重复: " + std::to_string(task->duplicate_count));
                                            task->last_reported_duplicate_seq = seq_num;
                                        }
                                    }
                                    else
                                    {
                                        // 第二级去重：基于消息内容指纹
                                        // 计算消息指纹
                                        std::string fingerprint = calculate_message_fingerprint(
                                                payload, seq_num, stream_data.chip_id(), stream_data.datetime());

                                        // 检查指纹是否已存在
                                        if(task->message_fingerprints.find(fingerprint) != task->message_fingerprints.end())
                                        {
                                            task->content_duplicate_count++;
                                            task->duplicate_count++;
                                            is_duplicate = true;
                                            // 记录内容重复但序列号不同的情况
                                            log_message(LOG_WARN, "检测到内容重复: " + task_key +
                                                        ", 序列号: " + std::to_string(seq_num) +
                                                        ", 指纹: " + fingerprint.substr(0, 16) + "...");
                                        }
                                        else
                                        {
                                            // 添加指纹到集合
                                            task->message_fingerprints.insert(fingerprint);
                                        }
                                    }

                                    // 如果是重复消息，跳过处理
                                    if(is_duplicate)
                                    {
                                        continue;
                                    }

                                    // 处理非重复消息
                                    MessageData data;
                                    data.chip_id = stream_data.chip_id();
                                    data.datetime = stream_data.datetime();
                                    data.sequence_number = seq_num;
                                    data.payload = payload;
                                    data.timestamp = stream_data.timestamp();
                                    // 保存消息指纹
                                    data.fingerprint = calculate_message_fingerprint(
                                                                       payload, seq_num, data.chip_id, data.datetime);
                                    // 添加到缓冲区
                                    buffer.messages.push_back(data);
                                    buffer.last_update = time(nullptr);
                                    // 保存到有序映射中
                                    sorted_messages[seq_num] = data;
                                    // 标记序列号为已接收
                                    received_sequences[seq_num] = true;
                                    // 更新统计信息
                                    message_count++;
                                    total_bytes += data.payload.size();

                                    // 更新序列号范围
                                    if(task->state.min_sequence == 0 || data.sequence_number < task->state.min_sequence)
                                    {
                                        task->state.min_sequence = data.sequence_number;
                                    }

                                    if(data.sequence_number > task->state.max_sequence)
                                    {
                                        task->state.max_sequence = data.sequence_number;
                                    }

                                    // 检查是否是最后一帧
                                    if(stream_data.is_last_frame())
                                    {
                                        log_message(LOG_INFO, "收到最后一帧: " + task_key + ", 序列号: " +
                                                    std::to_string(data.sequence_number));
                                        // 强制刷新缓冲区，确保最后一帧写入文件
                                        finished = true;
                                    }

                                    // 定期刷新缓冲区
                                    if(buffer.messages.size() >= 500 ||
                                            (time(nullptr) - buffer.last_update >= 3) ||
                                            finished)
                                    {
                                        // 排序消息
                                        if(config.sort_messages)
                                        {
                                            std::sort(buffer.messages.begin(), buffer.messages.end());
                                        }

                                        // 写入文件
                                        for(const auto &msg_data : buffer.messages)
                                        {
                                            output_file.write(msg_data.payload.data(), msg_data.payload.size());
                                        }

                                        // 确保数据写入磁盘
                                        output_file.flush();
                                        // 清空缓冲区
                                        buffer.messages.clear();
                                        // 更新任务状态
                                        task->state.processed_size = total_bytes;
                                        task->state.actual_messages = message_count;
                                        task->state.last_update = time(nullptr);

                                        // 检查是否完成
                                        if(total_bytes >= task->file_size)
                                        {
                                            // 构建详细的去重统计信息
                                            std::string dedup_stats;

                                            if(task->duplicate_count > 0)
                                            {
                                                dedup_stats = ", 跳过 " + std::to_string(task->duplicate_count) + " 条重复消息";

                                                // 添加详细的去重统计
                                                if(task->seq_duplicate_count > 0 || task->content_duplicate_count > 0)
                                                {
                                                    dedup_stats += " (序列号重复: " + std::to_string(task->seq_duplicate_count) +
                                                                   ", 内容重复: " + std::to_string(task->content_duplicate_count) + ")";
                                                }

                                                // 计算重复率
                                                double duplicate_rate = 0.0;

                                                if(task->total_processed_count > 0)
                                                {
                                                    duplicate_rate = (task->duplicate_count * 100.0) / task->total_processed_count;
                                                    dedup_stats += ", 重复率: " + std::to_string(duplicate_rate) + "%";
                                                }
                                            }

                                            log_message(LOG_INFO, "任务已完成: " + task_key +
                                                        ", 处理了 " + std::to_string(message_count) + " 条消息, " +
                                                        std::to_string(total_bytes) + " 字节" + dedup_stats);
                                            finished = true;
                                            break;
                                        }
                                    }
                                }
                            }
                            else
                            {
                                log_message(LOG_ERROR, "消息解析错误");
                            }

                            break;
                        }

                    case RdKafka::ERR__PARTITION_EOF:
                        // 已到达分区末尾，继续等待
                        break;

                    case RdKafka::ERR__TIMED_OUT:
                        // 超时，正常情况
                        {
                            // 检查是否已超时
                            time_t now = time(nullptr);
                            time_t elapsed = now - start_time;

                            // 每30秒记录一次等待状态，使用任务对象中的字段避免重复打印
                            if(elapsed % 30 == 0 && elapsed > 0 && message_count == 0 &&
                                    now != task->last_wait_log_time) // 确保同一秒内不会重复打印
                            {
                                // 获取消费者分配的分区信息
                                std::vector<RdKafka::TopicPartition *> partitions;
                                RdKafka::ErrorCode err = task->consumer->assignment(partitions);
                                std::stringstream ss;
                                ss << "等待消息中: " << task_key << ", 已等待 " << elapsed << " 秒";

                                if(err == RdKafka::ERR_NO_ERROR)
                                {
                                    ss << ", 分配的分区: ";

                                    for(const auto &tp : partitions)
                                    {
                                        ss << tp->topic() << "[" << tp->partition() << "] ";
                                        delete tp;
                                    }
                                }
                                else
                                {
                                    ss << ", 无法获取分区信息: " << RdKafka::err2str(err);
                                }

                                log_message(LOG_INFO, ss.str());
                                task->last_wait_log_time = now; // 更新任务的最后打印时间
                            }

                            // 超时时间从120秒增加到300秒（5分钟）
                            if(elapsed > 300 && message_count == 0)
                            {
                                log_message(LOG_WARN, "任务超时无消息: " + task_key + ", 已等待 " + std::to_string(elapsed) + " 秒");
                                // 尝试获取消费者元数据，帮助诊断问题
                                std::string metadata_str = "无法获取元数据";

                                try
                                {
                                    RdKafka::Metadata *metadata;
                                    RdKafka::ErrorCode err = task->consumer->metadata(true, nullptr, &metadata, 5000);

                                    if(err == RdKafka::ERR_NO_ERROR)
                                    {
                                        std::stringstream ss;
                                        ss << "Kafka集群: " << metadata->orig_broker_name() << " (" << metadata->orig_broker_id() << ")";
                                        ss << ", 主题数: " << metadata->topics()->size();
                                        metadata_str = ss.str();
                                        delete metadata;
                                    }
                                    else
                                    {
                                        metadata_str = "获取元数据失败: " + std::string(RdKafka::err2str(err));
                                    }
                                }
                                catch(const std::exception &e)
                                {
                                    metadata_str = "获取元数据异常: " + std::string(e.what());
                                }

                                log_message(LOG_INFO, "Kafka状态: " + metadata_str);
                                // 更新任务状态
                                task->state.status = TASK_RETRY;
                                std::string error_message = "获取消息超时";
                                task->state.error_message = error_message;
                                task->state.retry_count++;

                                // 确保安全关闭当前消费者
                                if(task->consumer)
                                {
                                    try
                                    {
                                        task->consumer->close();  // 先关闭消费者
                                        task->consumer.reset();   // 释放消费者资源
                                    }
                                    catch(const std::exception &e)
                                    {
                                        log_message(LOG_ERROR, "关闭消费者失败: " + std::string(e.what()));
                                    }
                                }

                                // 从活动任务中移除，再加入重试队列
                                {
                                    std::lock_guard<std::mutex> lock(tasks_mutex);
                                    active_tasks.erase(task_key);
                                }

                                if(task->state.retry_count >= config.retry_count)
                                {
                                    task->state.status = TASK_FAILED;
                                    error_message = "任务重试次数已达上限，标记为失败";
                                    task->state.error_message = error_message;
                                    log_message(LOG_ERROR, error_message + ": " + task_key);

                                    // 更新MySQL数据库中的设备信息状态
                                    if(app_config.use_mysql)
                                    {
                                        update_device_info_status(
                                                        task->chip_id,
                                                        task->datetime,
                                                        3, // 3表示处理失败
                                                        0, // 处理大小为0
                                                        "", // 没有校验和
                                                        error_message
                                        );
                                    }
                                }
                                else
                                {
                                    // 设置为非活动状态，等待重新入队
                                    task->active = false;
                                    // 重新入队等待重试
                                    {
                                        std::lock_guard<std::mutex> lock(queue_mutex);
                                        task_queue.push(task);
                                        queue_cv.notify_one();
                                    }
                                    log_message(LOG_INFO, "任务将重试: " + task_key + ", 重试次数: " +
                                                std::to_string(task->state.retry_count));
                                }

                                // 等待当前线程自然退出
                                return;
                            }
                        }
                        break;

                    default:
                        log_message(LOG_ERROR, "消费错误: " + msg->errstr());
                        break;
                }

                delete msg;
            }

            // 检查是否需要显示进度
            // 条件：1. 消息数量是5000的倍数 2. 距离上次打印至少5秒 3. 消息数量有变化
            time_t now = time(nullptr);

            if(config.verbose_level >= 2 && message_count > 0 &&
                    (message_count % 5000 == 0 || message_count == task->state.expected_messages) &&
                    (now - task->last_progress_log_time >= 5) &&
                    (message_count != task->last_progress_message_count))
            {
                double progress = (total_bytes * 100.0) / task->file_size;
                log_message(LOG_INFO, "任务进度: " + task_key + ": " +
                            std::to_string(static_cast<int>(progress)) + "%, " +
                            std::to_string(message_count) + " 条消息");
                // 更新最后打印时间和消息计数
                task->last_progress_log_time = now;
                task->last_progress_message_count = message_count;
            }
        }

        // 刷新剩余缓冲区
        if(!buffer.messages.empty())
        {
            // 排序消息
            if(config.sort_messages)
            {
                std::sort(buffer.messages.begin(), buffer.messages.end());
            }

            // 写入文件
            for(const auto &msg_data : buffer.messages)
            {
                output_file.write(msg_data.payload.data(), msg_data.payload.size());
            }

            // 确保数据写入磁盘
            output_file.flush();
            // 清空缓冲区
            buffer.messages.clear();
        }

        // 关闭文件
        output_file.close();
        // 检查文件是否成功写入
        struct stat file_stat;

        if(stat(task->output_path.c_str(), &file_stat) != 0 || file_stat.st_size == 0)
        {
            log_message(LOG_ERROR, "文件写入失败或大小为0: " + task->output_path);

            // 如果文件为空，将缓存的数据全部写入
            if(total_bytes > 0 && file_stat.st_size == 0)
            {
                log_message(LOG_INFO, "尝试重新写入文件...");
                // 尝试重新打开并写入所有数据
                std::ofstream recovery_file(task->output_path, std::ios::binary | std::ios::trunc);

                if(recovery_file.is_open())
                {
                    // 写入已处理的数据
                    if(!received_sequences.empty())
                    {
                        log_message(LOG_WARN, "检测到" + std::to_string(received_sequences.size()) + "条消息数据，但文件为空");
                        log_message(LOG_WARN, "文件恢复困难，请检查文件系统权限和磁盘空间");
                    }

                    recovery_file.close();
                }
            }
        }

        // 根据排序后的消息重新计算MD5校验和
        std::string calculated_md5 = calculate_md5_from_messages(sorted_messages);

        // 更新基本任务状态，无论成功失败
        if(task->state.status != TASK_FAILED && task->state.status != TASK_RETRY)
        {
            task->state.status = TASK_COMPLETED;
        }

        task->state.processed_size = total_bytes;
        task->state.actual_messages = message_count;
        // 定义处理状态和错误消息，用于更新MySQL
        int process_status = 1; // 默认为1表示成功
        std::string error_msg = "";

        // 校验和验证
        if(!task->checksum.empty())
        {
            // 记录更多关于校验和的信息
            log_message(LOG_INFO, "校验和验证: 文件=" + task->filename +
                        ", 大小=" + std::to_string(task->file_size) + " 字节" +
                        ", 实际处理=" + std::to_string(total_bytes) + " 字节" +
                        ", 消息数=" + std::to_string(message_count) +
                        ", 期望校验和=" + task->checksum);

            if(calculated_md5 == task->checksum)
            {
                log_message(LOG_INFO, "文件校验和匹配: " + calculated_md5);
                // 设置任务状态为已完成
                task->state.status = TASK_COMPLETED;
                process_status = 1; // 成功
            }
            else
            {
                // 记录更详细的错误信息
                log_message(LOG_ERROR, "文件校验和不匹配: 期望 " + task->checksum +
                            ", 计算得到 " + calculated_md5);
                // 检查是否有缺失帧
                bool missing_frames = false;

                for(uint64_t seq = task->state.min_sequence; seq <= task->state.max_sequence; seq++)
                {
                    if(sorted_messages.find(seq) == sorted_messages.end())
                    {
                        log_message(LOG_WARN, "校验和不匹配可能原因: 缺失序列号 " + std::to_string(seq));
                        missing_frames = true;
                    }
                }

                if(!missing_frames)
                {
                    log_message(LOG_INFO, "校验和不匹配但序列号完整，可能是数据传输过程中发生了变化");
                }

                // 检查文件大小是否匹配
                if(total_bytes != task->file_size)
                {
                    // 计算差异百分比
                    double size_diff = std::abs(static_cast<double>(total_bytes) - static_cast<double>(task->file_size));
                    double diff_percent = (size_diff / task->file_size) * 100.0;
                    log_message(LOG_WARN, "校验和不匹配可能原因: 文件大小不匹配，期望 " +
                                std::to_string(task->file_size) + " 字节，实际 " +
                                std::to_string(total_bytes) + " 字节，差异 " +
                                std::to_string(static_cast<int64_t>(total_bytes) - static_cast<int64_t>(task->file_size)) +
                                " 字节 (" + std::to_string(diff_percent) + "%)");
                    // 严格校验数据完整性，不忽略任何校验和不匹配
                    log_message(LOG_ERROR, "文件大小不匹配，数据完整性验证失败");

                    // 如果有重复消息，记录为可能的原因
                    if(task->duplicate_count > 0)
                    {
                        log_message(LOG_WARN, "校验和不匹配可能与重复消息有关，共检测到 " +
                                    std::to_string(task->duplicate_count) + " 条重复消息");
                    }

                    // 检查消息数量是否与预期一致
                    if(message_count != task->state.expected_messages && task->state.expected_messages > 0)
                    {
                        log_message(LOG_WARN, "校验和不匹配可能原因: 消息数量不匹配，期望 " +
                                    std::to_string(task->state.expected_messages) + " 条，实际 " +
                                    std::to_string(message_count) + " 条");
                    }
                }

                task->state.status = TASK_COMPLETED; // 依然标记为完成，但记录错误
                task->state.error_message = "校验和不匹配";
                process_status = 2; // 校验和错误
                error_msg = "校验和不匹配: 期望 " + task->checksum + ", 计算得到 " + calculated_md5;
            }
        }
        else
        {
            log_message(LOG_WARN, "未提供文件校验和，无法验证完整性");
            // 没有校验和，依然标记为完成
            task->state.status = TASK_COMPLETED;
            process_status = 1; // 成功，但没有校验和
        }

        // 更新最后更新时间
        task->state.last_update = time(nullptr);

        // 更新完成状态
        try
        {
            updateTaskState(task_key, task->state);

            // 更新MySQL数据库中的设备信息状态
            if(app_config.use_mysql)
            {
                update_device_info_status(
                                task->chip_id,
                                task->datetime,
                                process_status,
                                total_bytes,
                                calculated_md5,
                                error_msg
                );
            }
        }
        catch(const std::exception &e)
        {
            log_message(LOG_ERROR, "更新任务状态时出错: " + std::string(e.what()));
        }

        // 检查是否有缺失帧
        if(task->state.status == TASK_COMPLETED)
        {
            // 检查序列号是否连续
            bool missing_sequences = false;

            for(uint64_t seq = task->state.min_sequence; seq <= task->state.max_sequence; seq++)
            {
                if(received_sequences.find(seq) == received_sequences.end())
                {
                    log_message(LOG_WARN, "缺失序列号: " + task_key + ", 序列号: " + std::to_string(seq));
                    missing_sequences = true;
                }
            }

            if(missing_sequences)
            {
                log_message(LOG_WARN, "文件中存在缺失帧: " + task_key);
            }
            else
            {
                log_message(LOG_INFO, "文件帧序列完整: " + task_key);
            }

            // 文件已全部接收完成
            // 构建详细的去重统计信息
            std::string dedup_stats;

            if(task->duplicate_count > 0)
            {
                dedup_stats = ", 跳过 " + std::to_string(task->duplicate_count) + " 条重复消息";

                // 添加详细的去重统计
                if(task->seq_duplicate_count > 0 || task->content_duplicate_count > 0)
                {
                    dedup_stats += " (序列号重复: " + std::to_string(task->seq_duplicate_count) +
                                   ", 内容重复: " + std::to_string(task->content_duplicate_count) + ")";
                }

                // 计算重复率
                double duplicate_rate = 0.0;

                if(task->total_processed_count > 0)
                {
                    duplicate_rate = (task->duplicate_count * 100.0) / task->total_processed_count;
                    dedup_stats += ", 重复率: " + std::to_string(duplicate_rate) + "%";
                }
            }

            log_message(LOG_INFO, "文件接收完成: " + task_key + ", 处理了 " +
                        std::to_string(task->state.actual_messages) + " 条消息, " +
                        std::to_string(task->state.processed_size) + " 字节" + dedup_stats);
        }

        // 从活动任务中移除
        {
            std::lock_guard<std::mutex> lock(tasks_mutex);
            auto it = active_tasks.find(task_key);

            if(it != active_tasks.end())
            {
                // 确保在移除任务前正确关闭消费者
                try
                {
                    if(it->second && it->second->consumer)
                    {
                        it->second->consumer->close();
                        it->second->consumer.reset();
                    }
                }
                catch(const std::exception &e)
                {
                    log_message(LOG_ERROR, "关闭消费者异常: " + std::string(e.what()));
                }

                active_tasks.erase(it);
            }
        }
        // 确保线程可以正常退出
        task->active = false;
    }
    catch(const std::exception &e)
    {
        log_message(LOG_ERROR, "任务处理异常: " + task_key + ", " + e.what());
        // 更新任务状态
        task->state.status = TASK_FAILED;
        std::string error_message = "处理异常: " + std::string(e.what());
        task->state.error_message = error_message;
        updateTaskState(task_key, task->state);

        // 更新MySQL数据库中的设备信息状态
        if(app_config.use_mysql)
        {
            update_device_info_status(
                            task->chip_id,
                            task->datetime,
                            3, // 3表示处理失败
                            0, // 处理大小为0
                            "", // 没有校验和
                            error_message
            );
        }

        // 从活动任务中移除
        {
            std::lock_guard<std::mutex> lock(tasks_mutex);
            auto it = active_tasks.find(task_key);

            if(it != active_tasks.end())
            {
                try
                {
                    if(it->second && it->second->consumer)
                    {
                        it->second->consumer->close();
                        it->second->consumer.reset();
                    }
                }
                catch(...)
                {
                    // 忽略异常
                }

                active_tasks.erase(it);
            }
        }
        // 确保线程可以正常退出
        task->active = false;
    }

    // 为防止线程问题，在函数结束前进行最后的清理
    try
    {
        if(task && task->consumer)
        {
            task->consumer.reset();
        }
    }
    catch(...)
    {
        // 忽略任何异常
    }
}