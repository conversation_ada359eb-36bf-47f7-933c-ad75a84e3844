#ifndef MYSQL_POOL_H
#define MYSQL_POOL_H

#include <mysql/mysql.h>
#include <vector>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>
#include <functional>
#include <chrono>
#include <string>
#include <memory>

// 前向声明日志函数
void log_message(int level, const std::string &message);

// MySQL连接池类
class MySQLConnectionPool
{
  public:
    // 单例模式获取实例
    static MySQLConnectionPool &getInstance()
    {
        static MySQLConnectionPool instance;
        return instance;
    }

    // 初始化连接池
    bool initialize(const std::string &host, int port,
                    const std::string &user, const std::string &password,
                    const std::string &database, int pool_size = 3);

    // 关闭连接池
    void shutdown();

    // 异步执行SQL查询（不返回结果）
    void executeAsync(const std::string &sql,
                      std::function<void(bool, const std::string &)> callback);

    // 异步执行SQL查询（返回结果）
    void queryAsync(const std::string &sql,
                    std::function<void(MYSQL_RES *, const std::string &)> callback);

    // 检查连接池状态
    bool isInitialized() const
    {
        return initialized;
    }
    int getActiveConnections() const
    {
        return active_connections;
    }
    int getIdleConnections() const
    {
        return connection_pool.size();
    }

  private:
    // 私有构造函数（单例模式）
    MySQLConnectionPool();
    ~MySQLConnectionPool();

    // 禁止复制和赋值
    MySQLConnectionPool(const MySQLConnectionPool &) = delete;
    MySQLConnectionPool &operator=(const MySQLConnectionPool &) = delete;

    // 创建新连接
    MYSQL *createConnection();

    // 获取连接（有超时）
    MYSQL *getConnection(int timeout_seconds = 5);

    // 释放连接回池
    void releaseConnection(MYSQL *conn);

    // 工作线程函数
    void workerThread();

    // 连接保活线程函数
    void keepaliveThread();

    // 检查并刷新连接
    void refreshConnections();

    // 任务基类
    struct Task
    {
        virtual ~Task() {}
        virtual void execute() = 0;
    };

    // 查询任务（不返回结果）
    struct ExecuteTask : public Task
    {
        std::string sql;
        std::function<void(bool, const std::string &)> callback;

        ExecuteTask(const std::string &s, std::function<void(bool, const std::string &)> cb)
            : sql(s), callback(cb) {}

        void execute() override;
    };

    // 查询任务（返回结果）
    struct QueryTask : public Task
    {
        std::string sql;
        std::function<void(MYSQL_RES *, const std::string &)> callback;

        QueryTask(const std::string &s, std::function<void(MYSQL_RES *, const std::string &)> cb)
            : sql(s), callback(cb) {}

        void execute() override;
    };

    // 连接池
    std::queue<MYSQL *> connection_pool;
    std::mutex pool_mutex;
    std::condition_variable pool_condition;

    // 任务队列
    std::queue<std::shared_ptr<Task>> task_queue;
    std::mutex task_mutex;
    std::condition_variable task_condition;

    // 工作线程
    std::vector<std::thread> worker_threads;
    std::thread keepalive_thread;  // 连接保活线程
    std::atomic<bool> running;
    std::atomic<int> active_connections;

    // 连接参数
    std::string db_host;
    int db_port;
    std::string db_user;
    std::string db_password;
    std::string db_name;
    int max_pool_size;
    bool initialized;
};

#endif // MYSQL_POOL_H
