#ifndef EXPORTER_H
#define EXPORTER_H

#include <string>
#include <vector>
#include <queue>
#include <map>
#include <mutex>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <fstream>
#include <chrono>
#include <ctime>
#include <memory>
#include <functional>
#include <sys/stat.h>
#include <sys/types.h>
#include <librdkafka/rdkafkacpp.h>
#include "binary_stream.pb.h"
#include "kafka_config.h"
#include "common.h"
#include <openssl/md5.h>
#include <mysql/mysql.h> // MySQL客户端库

// 常量定义 - 避免和kafka_config.h中的冲突
// 使用不同的名称前缀EXP_
#define EXP_MAX_CONCURRENT_TASKS 64   // 最大并发任务数
#define EXP_DEFAULT_RETRY_COUNT 5     // 默认重试次数
#define EXP_DEFAULT_RETRY_DELAY 10000  // 默认重试延迟(毫秒)
#define EXP_STATE_SAVE_INTERVAL 60    // 状态保存间隔(秒)
// 使用kafka_config.h中的POLL_TIMEOUT_MS
// 使用kafka_config.h中的CONSUMER_GROUP_ID
#define EXP_STATE_FILE "../output/exporter/state.json" // 状态文件路径
#define EXP_LOG_DIR "../output/logs"  // 日志目录

// MySQL默认配置
// #define DEFAULT_MYSQL_HOST "dev.freedynamics.com.cn"
// #define DEFAULT_MYSQL_PORT 3306
// #define DEFAULT_MYSQL_DB "ifd_robot_db"
#define DEFAULT_MYSQL_HOST "*************"
#define DEFAULT_MYSQL_PORT 3306
#define DEFAULT_MYSQL_DB "ifd_robot_db"
#define DEFAULT_MYSQL_USER "ifd_robot"
#define DEFAULT_MYSQL_PASS "ifd123456"

// 日志级别
enum LogLevel
{
    LOG_ERROR = 0,
    LOG_WARN = 1,
    LOG_INFO = 2,
    LOG_DEBUG = 3,
    LOG_TRACE = 4
};

// 任务状态
enum TaskStatus
{
    TASK_PENDING,     // 等待执行
    TASK_RUNNING,     // 正在执行
    TASK_COMPLETED,   // 已完成
    TASK_FAILED,      // 失败
    TASK_RETRY        // 需要重试
};

// 线程状态
enum ThreadStatus
{
    THREAD_IDLE,      // 空闲
    THREAD_WORKING,   // 工作中
    THREAD_ERROR,     // 错误状态
    THREAD_STOPPING   // 正在停止
};

// 线程统计信息
struct ThreadStats
{
    std::string thread_id;           // 线程ID
    ThreadStatus status;             // 当前状态
    std::string current_task;        // 当前处理的任务
    time_t start_time;              // 线程启动时间
    time_t last_activity_time;      // 最后活动时间
    uint64_t tasks_processed;       // 已处理任务数
    uint64_t tasks_completed;       // 已完成任务数
    uint64_t tasks_failed;          // 失败任务数
    uint64_t bytes_processed;       // 已处理字节数
    uint64_t messages_processed;    // 已处理消息数
    uint64_t errors_count;          // 错误计数
    std::string last_error;         // 最后一个错误信息

    ThreadStats() : status(THREAD_IDLE), start_time(time(nullptr)),
        last_activity_time(time(nullptr)), tasks_processed(0),
        tasks_completed(0), tasks_failed(0), bytes_processed(0),
        messages_processed(0), errors_count(0) {}
};

// 文件处理状态
struct FileProcessingState
{
    std::string chip_id;
    std::string datetime;
    std::string filename;
    uint64_t expected_size;     // 预期文件大小(来自fd_info)
    uint64_t processed_size;    // 已处理大小
    uint64_t min_sequence;      // 最小序列号
    uint64_t max_sequence;      // 最大序列号
    uint64_t expected_messages; // 预期消息数
    uint64_t actual_messages;   // 实际消息数
    std::string checksum;       // 校验和
    time_t start_time;          // 开始处理时间
    time_t last_update;         // 最后更新时间
    TaskStatus status;          // 任务状态
    int retry_count;            // 重试次数
    std::string error_message;  // 错误信息

    FileProcessingState() :
        expected_size(0), processed_size(0),
        min_sequence(0), max_sequence(0),
        expected_messages(0), actual_messages(0),
        start_time(0), last_update(0),
        status(TASK_PENDING), retry_count(0) {}
};

// 数据获取任务
struct DataRetrievalTask
{
    std::string chip_id;
    std::string datetime;
    std::string filename;
    std::string topic;           // Kafka主题
    uint64_t file_size;          // 文件大小
    std::string checksum;        // 校验和
    std::atomic<bool> active;    // 任务是否活动
    std::thread thread;          // 处理线程
    std::unique_ptr<RdKafka::KafkaConsumer> consumer; // Kafka消费者
    std::string output_path;     // 输出文件路径
    FileProcessingState state;   // 文件处理状态
    MD5_CTX md5_context;         // MD5上下文，用于增量计算文件校验和
    bool md5_initialized;        // MD5上下文是否已初始化
    time_t last_wait_log_time;   // 最后一次打印等待日志的时间
    time_t last_progress_log_time; // 最后一次打印进度日志的时间
    uint64_t last_progress_message_count; // 上次打印进度时的消息计数

    // 重复消息跟踪
    bool duplicate_tracking_initialized; // 是否已初始化重复消息跟踪
    uint64_t duplicate_count;           // 重复消息计数
    uint64_t last_reported_duplicate_seq; // 上次报告的重复消息序列号

    // 增强的去重机制
    std::unordered_set<std::string> message_fingerprints; // 消息指纹集合，用于内容级去重
    time_t dedup_window_start;          // 去重窗口开始时间
    uint64_t seq_duplicate_count;       // 序列号重复计数
    uint64_t content_duplicate_count;   // 内容重复计数
    uint64_t total_processed_count;     // 总处理消息计数

    DataRetrievalTask() : file_size(0), active(false), md5_initialized(false),
        last_wait_log_time(0), last_progress_log_time(0), last_progress_message_count(0),
        duplicate_tracking_initialized(false), duplicate_count(0), last_reported_duplicate_seq(0),
        dedup_window_start(time(nullptr)), seq_duplicate_count(0),
        content_duplicate_count(0), total_processed_count(0) {}
};

// 应用配置
struct AppConfig
{
    int verbose_level;        // 详细程度
    std::string output_dir;   // 输出目录
    bool auto_fill;           // 自动填充缺失帧
    bool sort_messages;       // 排序消息
    int buffer_timeout;       // 缓冲区超时
    std::string state_file;   // 状态文件
    std::string log_file;     // 日志文件
    int log_level;            // 日志级别
    bool run_as_daemon;       // 作为守护进程运行
    std::string pid_file;     // PID文件路径
    std::string kafka_brokers; // Kafka服务器地址
    int max_tasks;            // 最大任务数
    int retry_count;          // 重试次数
    int retry_delay;          // 重试延迟(毫秒)
    std::string device_info_csv; // 设备信息CSV表格路径
    bool log_rotate_enable;   // 是否启用日志轮转
    int log_rotate_mode;      // 日志轮转模式：0=按日期，1=按大小
    int log_rotate_size;      // 日志轮转大小(MB)，当按大小轮转时使用
    int log_rotate_count;     // 保留的日志文件数量
    bool ignore_small_checksum_mismatch; // 是否忽略小的校验和不匹配
    double checksum_mismatch_threshold;  // 校验和不匹配的阈值（文件大小差异百分比）

    // MySQL数据库配置
    bool use_mysql;          // 是否使用MySQL
    std::string mysql_host;  // MySQL主机地址
    int mysql_port;          // MySQL端口
    std::string mysql_user;  // MySQL用户名
    std::string mysql_pass;  // MySQL密码
    std::string mysql_db;    // MySQL数据库名

    AppConfig() :
        verbose_level(0), auto_fill(false), sort_messages(true),
        buffer_timeout(300), log_level(LOG_INFO), run_as_daemon(false),
        max_tasks(EXP_MAX_CONCURRENT_TASKS), retry_count(EXP_DEFAULT_RETRY_COUNT),
        retry_delay(EXP_DEFAULT_RETRY_DELAY), log_rotate_enable(true),
        log_rotate_mode(0), log_rotate_size(100), log_rotate_count(10),
        ignore_small_checksum_mismatch(false), checksum_mismatch_threshold(0.0),
        use_mysql(true), mysql_port(DEFAULT_MYSQL_PORT)
    {
        output_dir = "../output/exporter";
        state_file = EXP_STATE_FILE;
        log_file = EXP_LOG_DIR "/exporter.log";
        pid_file = "../output/exporter.pid";
        kafka_brokers = KAFKA_BROKERS;
        device_info_csv = "../output/exporter/device_info.csv";
        // MySQL默认配置
        mysql_host = DEFAULT_MYSQL_HOST;
        mysql_user = DEFAULT_MYSQL_USER;
        mysql_pass = DEFAULT_MYSQL_PASS;
        mysql_db = DEFAULT_MYSQL_DB;
    }
};

// 任务管理器类
class TaskManager
{
  public:
    TaskManager(const AppConfig &config);
    ~TaskManager();

    // 添加任务
    bool addTask(const binary_stream::DeviceInfo &info);

    // 获取任务状态
    FileProcessingState getTaskState(const std::string &chip_id, const std::string &datetime);

    // 获取活动任务数
    int getActiveTaskCount() const;

    // 保存状态
    bool saveState() const;

    // 加载状态
    bool loadState();

    // 初始化
    bool initialize();

    // 停止所有任务
    void stopAllTasks();

    // 获取线程状态信息
    std::vector<ThreadStats> getThreadStats() const;

    // 打印线程状态报告
    void printThreadStatusReport();

    // 强制重置僵尸线程状态
    void forceResetZombieThreads();

  private:
    AppConfig config;
    std::map<std::string, std::shared_ptr<DataRetrievalTask>> active_tasks;
    std::queue<std::shared_ptr<DataRetrievalTask>> task_queue;
    std::map<std::string, FileProcessingState> completed_tasks;
    mutable std::mutex tasks_mutex;
    mutable std::mutex queue_mutex;
    mutable std::mutex state_mutex;
    std::condition_variable queue_cv;
    std::atomic<bool> running;
    std::thread task_dispatcher_thread;
    std::thread status_reporter_thread;  // 状态报告线程
    std::thread retry_failed_tasks_thread; // 重处理失败任务线程

    // 线程状态跟踪
    mutable std::mutex thread_stats_mutex;
    std::map<std::thread::id, ThreadStats> thread_stats;
    time_t last_status_report_time;

    // 重处理失败任务相关变量
    time_t last_retry_check_time;        // 上次检查重处理的时间
    std::map<std::string, int> task_retry_count; // 任务重试次数记录
    std::map<std::string, time_t> task_last_retry_time; // 任务上次重试时间
    mutable std::mutex retry_mutex;      // 重处理相关的互斥锁

    // 生成任务键(用于唯一标识任务)
    std::string generateTaskKey(const std::string &chip_id, const std::string &datetime);

    // 任务调度器线程函数
    void taskDispatcherFunc();

    // 任务处理线程函数
    void taskProcessingFunc(std::shared_ptr<DataRetrievalTask> task);

    // 创建Kafka消费者
    RdKafka::KafkaConsumer *createConsumer(const std::string &topic, const std::string &chip_id);

    // 确保输出目录存在
    bool ensureOutputDir(const std::string &chip_id);

    // 更新任务状态
    void updateTaskState(const std::string &task_key, const FileProcessingState &state);

    // 线程状态管理方法
    void updateThreadStatus(std::thread::id thread_id, ThreadStatus status, const std::string &current_task = "");
    void updateThreadStats(std::thread::id thread_id, uint64_t bytes_processed, uint64_t messages_processed);
    void recordThreadError(std::thread::id thread_id, const std::string &error_msg);
    void statusReporterFunc();  // 状态报告线程函数

    // 重处理失败任务相关方法
    bool areAllThreadsIdle() const;  // 检查是否所有线程都空闲
    void retryFailedTasksFunc();     // 重处理失败任务线程函数
    std::vector<std::pair<std::string, std::string>> getFailedTasksFromDB(); // 从数据库获取失败任务
    bool shouldRetryTask(const std::string &chip_id, const std::string &datetime); // 判断是否应该重试任务
    void cleanupExpiredThreads(); // 清理过期的线程状态
};

// 消息处理类
struct MessageData
{
    std::string chip_id;
    std::string datetime;
    std::string payload;
    uint64_t sequence_number;
    time_t timestamp;
    std::string fingerprint;    // 消息内容的哈希指纹，用于去重

    // 排序操作符
    bool operator<(const MessageData &other) const
    {
        return sequence_number < other.sequence_number;
    }
};

// 缓冲区类
struct FileBuffer
{
    std::vector<MessageData> messages;
    time_t last_update;

    FileBuffer() : last_update(time(nullptr)) {}
};

// 函数声明
void log_message(LogLevel level, const std::string &message);
AppConfig parse_command_line(int argc, char **argv);
void signal_handler(int sig);
bool daemonize();
void print_usage(const char *program_name);
bool save_state(const TaskManager &manager);
bool load_state(TaskManager &manager);
bool export_device_info_to_csv(const binary_stream::DeviceInfo &device_info);
bool init_mysql_connection();
void close_mysql_connection();
bool save_device_info_to_mysql(const binary_stream::DeviceInfo &device_info);
bool update_device_info_status(const std::string &chip_id, const std::string &datetime,
                               int status, uint64_t processed_size,
                               const std::string &actual_checksum, const std::string &error_message);

#endif // EXPORTER_H