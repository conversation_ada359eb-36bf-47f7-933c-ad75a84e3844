# Kafka二进制流数据传输系统

本系统实现了通过Kafka有序地传输二进制流数据，生产者(Producer)将二进制数据流通过protobuf序列化后发送到Kafka服务器，消费者(Consumer)从Kafka获取数据，反序列化后保存到本地文件。

## 功能特点

- 使用librdkafka库实现与Kafka的高效通信
- 使用protobuf序列化二进制数据，确保跨平台兼容性
- 通过芯片ID作为Key区分不同数据源
- **支持变长二进制数据帧**，更高效利用网络带宽
- 通过序列号保证数据顺序性
- 通过校验和保证数据完整性
- 多个生产者可同时向服务器发送数据
- 消费者能区分并保存来自不同生产者的数据

## 系统依赖

- librdkafka (Kafka客户端库)
- protobuf-c (Protocol Buffers C实现)
- GCC编译器和标准C库

## 安装依赖

```bash
sudo apt-get update
sudo apt-get install -y librdkafka-dev libprotobuf-c-dev protobuf-c-compiler
```

## 编译

```bash
make clean
make
```

## 使用方法

### 生产者

生产者通过将二进制数据流序列化并发送到Kafka服务器：

```bash
# 使用默认芯片ID
./producer

# 指定自定义芯片ID（最多16字节）
./producer a1b2c3d4e5f67890
```

命令行参数：
- 无参数：使用默认芯片ID "b124ee90bffe90ce"
- 一个参数：使用指定的芯片ID（最多16字节）

生产者生成的原始数据将保存在`output_producer`目录下，文件名格式为`<芯片ID>_<时间戳>_original.bin`。

**数据格式**：
- 每个数据帧长度动态变化，范围在128~1024字节之间
- 每个帧前4个字节是固定标记（AABBCCDD）
- 接下来4个字节包含序列号
- 帧中包含长度信息，便于验证

### 消费者

消费者从Kafka获取数据，并将其合并保存到本地：

```bash
./consumer
```

数据将按照芯片ID合并保存到`output`目录下，文件名格式为`<芯片ID>_<时间戳>.bin`。只有当所有帧都完整接收时，才会保存合并文件。

## 数据格式

二进制流数据使用Protocol Buffers序列化，消息格式如下：

```protobuf
message StreamData {
  uint64 sequence_number = 1;  // 帧序列号
  bytes payload = 2;           // 二进制负载数据
  string chip_id = 3;          // 芯片ID
  uint32 checksum = 4;         // 校验和
  uint64 timestamp = 5;        // 时间戳
  bool is_last_frame = 6;      // 是否为最后一帧
  uint32 payload_length = 7;   // 数据长度信息
}
```

## 文件存储格式

为了支持变长数据帧，原始数据文件和合并后的文件都使用以下格式：
- 每个数据帧前有4字节长度信息（网络字节序）
- 然后是对应长度的数据帧内容
- 这种格式确保即使帧长度不同，也能正确分割和重组数据

## 清理

项目提供了两种清理方式：

```bash
# 清理所有生成的文件和数据目录
make clean

# 只清理编译生成的文件，保留数据目录
make cleanbin
```

## 注意事项

- 确保Kafka服务器可访问
- 如需修改服务器地址，请更新producer.c和consumer.c中的bootstrap.servers配置
- 默认情况下，每个数据帧的长度在128~1024字节之间变化，可在producer.c中调整
- 按Ctrl+C可安全终止生产者或消费者

## 故障排除

- 如果连接Kafka失败，请检查服务器地址和防火墙设置
- 如果数据接收不完整，可能是由于网络问题或Kafka配置问题
- 检查是否有足够的存储空间保存接收到的数据 







# Kafka导出器 (Kafka Exporter)

这个工具从Kafka主题中消费数据帧，按芯片ID组织，并按序列号保存到文件中。

## 功能

- 将数据帧写入按芯片ID命名的文件中
- 自动排序功能：按照序列号顺序写入数据，即使消息乱序到达
- 自动填充功能：可以自动填充缺失的序列号区间
- 过滤功能：只处理特定的chip_id、序列号范围或时间范围
- 恢复功能：保存/加载处理状态，可以从之前导出的位置继续

## 编译

确保已安装librdkafka库：

```
# Debian/Ubuntu
sudo apt-get install librdkafka-dev

# CentOS/RHEL
sudo yum install librdkafka-devel
```

编译程序：

```
make
```

## 使用方法

```
./exporter [options]
```

### 选项

- `-b <brokers>`: Kafka代理列表 (默认: localhost:9092)
- `-t <topic>`: Kafka主题 (默认: data-stream)
- `-g <group>`: 消费者组ID (默认: exporter-group)
- `-o <dir>`: 输出目录 (默认: ./output)
- `-p <partition>`: 指定分区 (默认: 自动分配)
- `-s`: 启用自动排序 (按序列号)
- `-f`: 启用自动填充 (生成缺失序列号的空帧)
- `-v`: 增加详细输出 (可多次使用)
- `-r <file>`: 状态恢复文件
- `-i <secs>`: 状态保存间隔 (秒)
- `-c <id>`: 过滤特定芯片ID
- `-n <num>`: 过滤最小序列号
- `-N <num>`: 过滤最大序列号
- `-T <time>`: 过滤起始时间 (UNIX时间戳)
- `-U <time>`: 过滤结束时间 (UNIX时间戳)
- `-h`: 显示帮助
- `-R`: 重置数据消费位置

## 示例

### 基本用法

```
./exproter -b localhost:9092 -t my-data-topic -o /data/output
```

### 启用排序和填充

```
./exproter -s -f -o /data/output
```

### 从特定状态恢复

```
./exproter -r state.json -i 60
```

### 只处理特定芯片ID和序列号范围

```
./exproter -c chip123 -n 1000 -N 2000
```

### 只处理特定时间范围的消息

```
./exproter -T 1620000000 -U 1620086400
```

## 输出

程序将在输出目录中创建按芯片ID命名的文件，每个文件包含该芯片的所有数据帧，按序列号排序。 



# Kafka ARM版本编译指南

本文档说明如何为ARM平台（如树莓派、嵌入式设备等）编译Kafka生产者和导出工具。

## 准备工作

1. 确保安装了ARM交叉编译工具链：`aarch64-buildroot-linux-gnu-g++`

2. 需要ARM平台编译的librdkafka++和protobuf库：
   - `librdkafka++.a` 或 `librdkafka++.so`（librdkafka的C++客户端库）
   - `librdkafka.a` 或 `librdkafka.so`（librdkafka的基础库）
   - `libprotobuf.a`（Protocol Buffers库）
   - 相应的头文件

## 目录结构

使用以下目录结构存放ARM库文件：

```
arm_libs/
  ├── lib/                 # 存放库文件
  │   ├── librdkafka++.a   # 或 librdkafka++.so
  │   ├── librdkafka.a     # 或 librdkafka.so
  │   └── libprotobuf.a
  └── include/             # 存放头文件
      ├── librdkafka/
      │   └── rdkafkacpp.h
      └── google/
          └── protobuf/
              └── ...
```

## 静态库与动态库

项目支持两种类型的库文件：

1. **静态库 (.a)**
   - 优点：编译出的程序不需要外部依赖，可以直接运行
   - 缺点：程序体积较大

2. **动态库 (.so)**
   - 优点：程序体积较小
   - 缺点：在目标ARM平台上运行时需要安装相应的动态库

**重要提示：** 如果使用动态库版本的librdkafka++.so，必须同时提供librdkafka.so，因为前者依赖于后者。

## 准备环境

可以使用提供的脚本自动准备环境：

```bash
./prepare_arm_env.sh
```

该脚本会创建必要的目录结构，并检查是否已有所需的库文件。

## 手动准备

1. 创建目录结构：
   ```bash
   mkdir -p arm_libs/lib arm_libs/include
   ```

2. 将编译好的ARM平台库文件复制到`arm_libs/lib`目录：
   ```bash
   # 静态库方式（推荐）
   cp /path/to/arm/libs/librdkafka++.a arm_libs/lib/
   cp /path/to/arm/libs/librdkafka.a arm_libs/lib/
   cp /path/to/arm/libs/libprotobuf.a arm_libs/lib/
   
   # 或动态库方式
   cp /path/to/arm/libs/librdkafka++.so arm_libs/lib/
   cp /path/to/arm/libs/librdkafka.so arm_libs/lib/
   cp /path/to/arm/libs/libprotobuf.a arm_libs/lib/
   ```

3. 复制必要的头文件到`arm_libs/include`目录：
   ```bash
   cp -r /path/to/arm/include/librdkafka arm_libs/include/
   cp -r /path/to/arm/include/google arm_libs/include/
   ```

## 编译

完成准备工作后，使用以下命令编译ARM版本：

```bash
make arm
```

编译成功后，将在当前目录生成以下可执行文件：
- `producer-arm`：Kafka生产者（ARM版本）
- `exproter-arm`：Kafka导出工具（ARM版本）

## 部署到ARM设备

将编译好的可执行文件复制到目标ARM设备：

```bash
scp producer-arm exproter-arm user@arm-device:/path/to/destination/
```

如果使用动态库编译，还需确保目标设备上存在相应的动态库：

```bash
scp arm_libs/lib/librdkafka++.so arm_libs/lib/librdkafka.so user@arm-device:/path/to/lib/
```

## 运行

在ARM设备上运行：

```bash
# 生产者
./producer-arm [选项]

# 导出工具
./exproter-arm [选项]
```

## 故障排除

1. **编译错误 - 未定义的引用**：
   - 错误信息: `undefined reference to 'rd_kafka_xxx'`
   - 原因: librdkafka++.so依赖于librdkafka.so，但后者未找到
   - 解决方案:
     - 确保arm_libs/lib目录中同时包含librdkafka++和librdkafka库文件
     - 最好使用静态库(.a)代替动态库(.so)，这样可以避免依赖问题

2. **"库xxx未找到"警告**：
   - 错误信息: `warning: librdkafka.so.1, needed by ./arm_libs/lib/librdkafka++.so, not found`
   - 解决方案: 
     - 使用静态链接 `-Wl,-Bstatic -lrdkafka++ -lrdkafka -Wl,-Bdynamic`
     - 确保提供librdkafka.so.1或符号链接

3. **找不到头文件**：
   - 错误信息: `fatal error: librdkafka/rdkafkacpp.h: No such file or directory`
   - 解决方案: 确保头文件放在正确的arm_libs/include目录结构中

4. **运行时错误**：
   - 确保目标ARM设备有必要的动态库支持：glibc、libstdc++等
   - 如果使用动态库编译，需要在目标设备上安装librdkafka++.so和librdkafka.so

## 清理

清理编译生成的文件：

```bash
make clean
```

清理ARM库目录：

```bash
make clean-arm-libs
```