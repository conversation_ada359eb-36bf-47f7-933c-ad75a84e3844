# libprotobuf.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6 Debian-2.4.6-2
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libprotobuf.so.14'

# Names of this library.
library_names='libprotobuf.so.14.0.0 libprotobuf.so.14 libprotobuf.so'

# The name of the static archive.
old_library='libprotobuf.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' -lpthread -lz'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libprotobuf.
current=14
age=0
revision=0

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/workspace/Gundam_ws/gundam_manager/build/pkg_manager_rk3308_cross/external/Install/lib'
