CC = gcc
CXX = g++
CXXFLAGS = -Wall -Werror -g -O2 -std=c++17 -pthread -I$(SRC_DIR) -Wno-deprecated-declarations
# C++版本编译标志
# 注意：使用 /usr/local/lib 目录下的静态 protobuf 库，以匹配 protoc 3.4.0 版本
# 否则会出现 "undefined reference to google::protobuf::..." 错误
CXX_LDFLAGS = -lrdkafka++ -pthread -lz -ldl -lrt /usr/local/lib/libprotobuf.a -lm -lcrypto -lmysqlclient

# ARM交叉编译工具链
ARM_CXX := aarch64-buildroot-linux-gnu-g++
ARM_CXXFLAGS := -std=c++11 -Wall -Os -I./libs/include -Wno-deprecated-declarations
ARM_LIBS_DIR := ./libs
# 确保使用静态库并正确处理依赖关系
ARM_CXX_LDFLAGS := -L$(ARM_LIBS_DIR)/lib -Wl,-Bstatic -lrdkafka++ -lrdkafka -lprotobuf -Wl,-Bdynamic -lpthread -lz -lrt -ldl -lcrypto -static-libstdc++ -static-libgcc
# ARM平台strip工具
ARM_STRIP := aarch64-buildroot-linux-gnu-strip

# 目录结构
SRC_DIR := src
BIN_DIR := bin
OUTPUT_DIR := output

# 目标文件
TARGETS = $(BIN_DIR)/producer  $(BIN_DIR)/exporter
ARM_TARGETS = $(BIN_DIR)/producer-arm

# 添加common.cpp到源文件列表
COMMON_SRC := $(SRC_DIR)/common.cpp
PROTO_SRC := $(SRC_DIR)/binary_stream.pb.cc

all: dirs $(TARGETS) $(ARM_TARGETS)

host: dirs $(TARGETS)
arm: dirs $(ARM_TARGETS)

# 创建必要的目录
dirs:
	mkdir -p $(BIN_DIR) $(OUTPUT_DIR)/producer $(OUTPUT_DIR)/exporter

$(BIN_DIR)/producer: $(SRC_DIR)/producer.cpp $(SRC_DIR)/binary_stream.pb.cc $(SRC_DIR)/kafka_config.h
	$(CXX) $(CXXFLAGS) -o $@ $(SRC_DIR)/producer.cpp $(SRC_DIR)/binary_stream.pb.cc $(COMMON_SRC) $(CXX_LDFLAGS)
	$(ARM_CXX) $(ARM_CXXFLAGS) -o $(BIN_DIR)/producer-arm $(SRC_DIR)/producer.cpp $(SRC_DIR)/binary_stream.pb.cc $(COMMON_SRC) $(ARM_CXX_LDFLAGS)


$(BIN_DIR)/exporter: $(SRC_DIR)/exporter.cpp $(SRC_DIR)/binary_stream.pb.cc $(SRC_DIR)/kafka_config.h $(SRC_DIR)/task_manager.cpp $(SRC_DIR)/exporter.h $(SRC_DIR)/mysql_pool.cpp $(SRC_DIR)/mysql_pool.h $(SRC_DIR)/fixed_thread_pool.cpp $(SRC_DIR)/fixed_thread_pool.h $(COMMON_SRC)
	$(CXX) $(CXXFLAGS) -o $@ $(SRC_DIR)/exporter.cpp $(SRC_DIR)/binary_stream.pb.cc $(SRC_DIR)/task_manager.cpp $(SRC_DIR)/mysql_pool.cpp $(SRC_DIR)/fixed_thread_pool.cpp $(COMMON_SRC) $(CXX_LDFLAGS)

# Protobuf生成规则
$(SRC_DIR)/binary_stream.pb.cc $(SRC_DIR)/binary_stream.pb.h: $(SRC_DIR)/binary_stream.proto
	cd $(SRC_DIR) && protoc --cpp_out=. binary_stream.proto

# 清理规则
clean:
	rm -f $(TARGETS) $(ARM_TARGETS) $(SRC_DIR)/*.o $(SRC_DIR)/*.pb.*
	rm -rf $(BIN_DIR)/* $(OUTPUT_DIR)/* logs tools/output

# ARM可执行文件瘦身
arm-strip: $(ARM_TARGETS)
	$(ARM_STRIP) $(BIN_DIR)/producer-arm
	@echo "Stripped ARM binaries successfully"

.PHONY: all arm clean host arm-strip dirs
